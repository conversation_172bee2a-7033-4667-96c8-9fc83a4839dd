@echo off
chcp 65001 > nul
title Qatar Car Showroom System
echo.
echo ========================================
echo    Qatar Car Showroom System
echo ========================================
echo.

cd /d "%~dp0"

echo Current Directory: %CD%
echo.

echo Checking system files...
if not exist "working_app.py" (
    echo ERROR: working_app.py not found!
    pause
    exit /b 1
)

echo All files found
echo.

echo Starting Python system...
echo.

REM Try different Python commands
python --version > nul 2>&1
if errorlevel 1 (
    echo Python not found in PATH, trying py command...
    py --version > nul 2>&1
    if errorlevel 1 (
        echo ERROR: Python is not installed or not in PATH
        echo Please install Python from python.org
        pause
        exit /b 1
    ) else (
        echo Using py command...
        py working_app.py
    )
) else (
    echo Using python command...
    python working_app.py
)

echo.
echo System stopped.
pause
