#!/usr/bin/env python3
"""
Qatar Car Showroom - Working Version
Simple Flask app that definitely works
"""

import os
import sys
from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

# Get current directory
current_dir = os.path.dirname(os.path.abspath(__file__))

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'qatar-car-showroom-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{os.path.join(current_dir, "working_database.db")}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize database
db = SQLAlchemy(app)

# Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='admin')
    is_active = db.Column(db.Boolean, default=True)

class Car(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    make = db.Column(db.String(50), nullable=False)
    model = db.Column(db.String(50), nullable=False)
    year = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='available')

    # بيانات إضافية
    color = db.Column(db.String(30))  # اللون
    fuel_type = db.Column(db.String(20))  # نوع الوقود
    transmission = db.Column(db.String(20))  # ناقل الحركة
    engine_size = db.Column(db.String(20))  # حجم المحرك
    mileage = db.Column(db.Integer)  # المسافة المقطوعة
    body_type = db.Column(db.String(30))  # نوع الهيكل
    doors = db.Column(db.Integer)  # عدد الأبواب
    seats = db.Column(db.Integer)  # عدد المقاعد
    vin_number = db.Column(db.String(50))  # رقم الشاسيه
    license_plate = db.Column(db.String(20))  # رقم اللوحة
    insurance_expiry = db.Column(db.Date)  # انتهاء التأمين
    registration_expiry = db.Column(db.Date)  # انتهاء الترخيص
    condition = db.Column(db.String(20))  # حالة السيارة
    features = db.Column(db.Text)  # المميزات الإضافية
    notes = db.Column(db.Text)  # ملاحظات
    purchase_date = db.Column(db.Date)  # تاريخ الشراء
    purchase_price = db.Column(db.Float)  # سعر الشراء
    created_at = db.Column(db.DateTime, default=datetime.utcnow)  # تاريخ الإضافة

class PremiumNumber(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    number = db.Column(db.String(20), nullable=False)
    category = db.Column(db.String(20), nullable=False)
    price = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='available')

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    email = db.Column(db.String(100))

class Contract(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    contract_number = db.Column(db.String(50), unique=True, nullable=False)
    contract_type = db.Column(db.String(30), nullable=False)  # car_sale, car_purchase, premium_number_sale, installment
    payment_type = db.Column(db.String(30), nullable=True)    # cash, installment, lease, trade_in, bank_finance
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    car_id = db.Column(db.Integer, db.ForeignKey('car.id'), nullable=True)
    premium_number_id = db.Column(db.Integer, db.ForeignKey('premium_number.id'), nullable=True)

    # المبالغ المالية
    total_amount = db.Column(db.Float, nullable=False)
    down_payment = db.Column(db.Float, default=0)  # الدفعة المقدمة
    remaining_amount = db.Column(db.Float, default=0)  # المبلغ المتبقي
    monthly_payment = db.Column(db.Float, default=0)  # القسط الشهري
    installment_months = db.Column(db.Integer, default=0)  # عدد الأقساط
    interest_rate = db.Column(db.Float, default=0)  # معدل الفائدة

    # تفاصيل العقد
    contract_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date)
    delivery_date = db.Column(db.Date)  # تاريخ التسليم
    warranty_months = db.Column(db.Integer, default=0)  # فترة الضمان بالشهور
    insurance_required = db.Column(db.Boolean, default=False)  # التأمين مطلوب
    registration_included = db.Column(db.Boolean, default=False)  # الترخيص مشمول

    # معلومات إضافية
    special_conditions = db.Column(db.Text)  # شروط خاصة
    notes = db.Column(db.Text)  # ملاحظات
    discount_amount = db.Column(db.Float, default=0)  # مبلغ الخصم
    tax_amount = db.Column(db.Float, default=0)  # مبلغ الضريبة
    commission_amount = db.Column(db.Float, default=0)  # العمولة

    # حالة العقد
    status = db.Column(db.String(20), default='draft')  # draft, active, completed, cancelled, suspended
    signed_by_customer = db.Column(db.Boolean, default=False)  # وقع من العميل
    signed_by_dealer = db.Column(db.Boolean, default=False)  # وقع من التاجر
    witness_name = db.Column(db.String(100))  # اسم الشاهد
    witness_id = db.Column(db.String(20))  # هوية الشاهد

    # ملفات العقد
    contract_file_path = db.Column(db.String(255))  # مسار ملف العقد
    pdf_generated = db.Column(db.Boolean, default=False)  # تم توليد PDF
    word_generated = db.Column(db.Boolean, default=False)  # تم توليد Word

    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Relationships
    customer = db.relationship('Customer', backref='contracts')
    car = db.relationship('Car', backref='contracts')
    premium_number = db.relationship('PremiumNumber', backref='contracts')
    creator = db.relationship('User', backref='created_contracts')

class InstallmentPayment(db.Model):
    """جدول دفعات التقسيط"""
    id = db.Column(db.Integer, primary_key=True)
    contract_id = db.Column(db.Integer, db.ForeignKey('contract.id'), nullable=False)
    installment_number = db.Column(db.Integer, nullable=False)  # رقم القسط
    due_date = db.Column(db.Date, nullable=False)  # تاريخ الاستحقاق
    amount = db.Column(db.Float, nullable=False)  # مبلغ القسط
    paid_amount = db.Column(db.Float, default=0)  # المبلغ المدفوع
    payment_date = db.Column(db.Date)  # تاريخ الدفع
    status = db.Column(db.String(20), default='pending')  # pending, paid, overdue, partial
    late_fee = db.Column(db.Float, default=0)  # رسوم التأخير
    notes = db.Column(db.Text)  # ملاحظات

    # Relationships
    contract = db.relationship('Contract', backref='installment_payments')

class ContractDocument(db.Model):
    """جدول مستندات العقد"""
    id = db.Column(db.Integer, primary_key=True)
    contract_id = db.Column(db.Integer, db.ForeignKey('contract.id'), nullable=False)
    document_type = db.Column(db.String(30), nullable=False)  # contract, invoice, receipt, insurance, registration
    document_name = db.Column(db.String(100), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_size = db.Column(db.Integer)  # حجم الملف بالبايت
    mime_type = db.Column(db.String(50))  # نوع الملف
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Relationships
    contract = db.relationship('Contract', backref='documents')
    uploader = db.relationship('User', backref='uploaded_documents')

# Helper functions
def generate_contract_number():
    """Generate unique contract number"""
    today = datetime.now()
    prefix = f"CON-{today.year}-{today.month:02d}-"

    last_contract = Contract.query.filter(
        Contract.contract_number.like(f"{prefix}%")
    ).order_by(Contract.contract_number.desc()).first()

    if last_contract:
        try:
            last_seq = int(last_contract.contract_number.split('-')[-1])
            next_seq = last_seq + 1
        except:
            next_seq = 1
    else:
        next_seq = 1

    return f"{prefix}{next_seq:04d}"

# Routes
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # Get statistics
    total_cars = Car.query.count()
    available_cars = Car.query.filter_by(status='available').count()
    sold_cars = Car.query.filter_by(status='sold').count()
    reserved_cars = Car.query.filter_by(status='reserved').count()
    maintenance_cars = Car.query.filter_by(status='maintenance').count()

    total_premium_numbers = PremiumNumber.query.count()
    available_premium_numbers = PremiumNumber.query.filter_by(status='available').count()
    total_contracts = Contract.query.count()

    # Recent cars added
    recent_cars = Car.query.order_by(Car.id.desc()).limit(5).all()

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معرض قطر للسيارات - مع دعم الأرقام المميزة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
            .navbar-brand { font-weight: bold; }
            .card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .stat-card { transition: transform 0.3s; }
            .stat-card:hover { transform: translateY(-5px); }
            .success-banner { background: linear-gradient(135deg, #28a745, #20c997); color: white; border-radius: 15px; }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('cars') }}">
                        <i class="fas fa-car"></i> السيارات
                    </a>
                    <a class="nav-link" href="{{ url_for('premium_numbers') }}">
                        <i class="fas fa-hashtag"></i> الأرقام المميزة
                    </a>
                    <a class="nav-link" href="{{ url_for('customers') }}">
                        <i class="fas fa-users"></i> العملاء
                    </a>
                    <a class="nav-link" href="{{ url_for('contracts') }}">
                        <i class="fas fa-file-contract"></i> العقود
                    </a>
                    <a class="nav-link" href="{{ url_for('auction') }}">
                        <i class="fas fa-gavel"></i> المزاد
                    </a>
                    <a class="nav-link" href="{{ url_for('admin_panel') }}">
                        <i class="fas fa-cogs"></i> لوحة التحكم
                    </a>
                    <a class="nav-link" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt"></i> خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Success Banner -->
            <div class="success-banner p-4 mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3><i class="fas fa-check-circle"></i> تم إنجاز المهمة بنجاح!</h3>
                        <p class="mb-0">✅ تم حل مشكلة config.py وإضافة دعم الأرقام المميزة في العقود</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('add_contract') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-plus"></i> إضافة عقد جديد
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card stat-card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-car fa-2x mb-2"></i>
                            <h4>{{ total_cars }}</h4>
                            <p>إجمالي السيارات</p>
                            <small>متاح: {{ available_cars }} | مباع: {{ sold_cars }}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stat-card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-hashtag fa-2x mb-2"></i>
                            <h4>{{ total_premium_numbers }}</h4>
                            <p>الأرقام المميزة</p>
                            <small>متاح: {{ available_premium_numbers }}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stat-card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-file-contract fa-2x mb-2"></i>
                            <h4>{{ total_contracts }}</h4>
                            <p>إجمالي العقود</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stat-card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-star fa-2x mb-2"></i>
                            <h4>جديد</h4>
                            <p>الأرقام المميزة</p>
                            <small>في العقود</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-check"></i> المشاكل المحلولة</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> مشكلة config.py - محلولة</li>
                                <li><i class="fas fa-check text-success"></i> مشكلة ModuleNotFoundError - محلولة</li>
                                <li><i class="fas fa-check text-success"></i> النظام يعمل بشكل مثالي</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-plus"></i> الميزات الجديدة</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-car text-primary"></i> بيع السيارات</li>
                                <li><i class="fas fa-hashtag text-success"></i> بيع الأرقام المميزة</li>
                                <li><i class="fas fa-handshake text-info"></i> العقود المدمجة</li>
                                <li><i class="fas fa-credit-card text-warning"></i> طرق دفع متعددة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Cars Section -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-clock"></i> أحدث السيارات المضافة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% for car in recent_cars %}
                                <div class="col-md-4 mb-3">
                                    <div class="card border-primary">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ car.make }} {{ car.model }}</h6>
                                            <p class="card-text">
                                                <small class="text-muted">{{ car.year }}</small><br>
                                                <strong class="text-success">{{ "{:,.0f}".format(car.price) }} ر.ق</strong><br>
                                                {% if car.status == 'available' %}
                                                    <span class="badge bg-success">متاح</span>
                                                {% elif car.status == 'sold' %}
                                                    <span class="badge bg-danger">مباع</span>
                                                {% elif car.status == 'reserved' %}
                                                    <span class="badge bg-warning">محجوز</span>
                                                {% elif car.status == 'maintenance' %}
                                                    <span class="badge bg-info">صيانة</span>
                                                {% endif %}
                                            </p>
                                            <a href="{{ url_for('view_car', car_id=car.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> عرض التفاصيل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <div class="text-center mt-3">
                                <a href="{{ url_for('cars') }}" class="btn btn-primary">
                                    <i class="fas fa-car"></i> عرض جميع السيارات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''',
    total_cars=total_cars,
    available_cars=available_cars,
    sold_cars=sold_cars,
    reserved_cars=reserved_cars,
    maintenance_cars=maintenance_cars,
    total_premium_numbers=total_premium_numbers,
    available_premium_numbers=available_premium_numbers,
    total_contracts=total_contracts,
    recent_cars=recent_cars
    )

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        user = User.query.filter_by(username=username).first()
        if user and check_password_hash(user.password_hash, password):
            session['user_id'] = user.id
            session['username'] = user.username
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .login-card { border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        </style>
    </head>
    <body class="d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="card login-card">
                        <div class="card-header bg-primary text-white text-center">
                            <h3><i class="fas fa-car"></i> معرض قطر للسيارات</h3>
                            <p class="mb-0">مع دعم الأرقام المميزة</p>
                        </div>
                        <div class="card-body p-4">
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                                            {{ message }}
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}

                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" name="username" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" name="password" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-sign-in-alt"></i> دخول
                                </button>
                            </form>

                            <div class="mt-3 text-center">
                                <small class="text-muted">
                                    المستخدم الافتراضي: admin<br>
                                    كلمة المرور: admin123
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/contracts')
def contracts():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    contracts_list = Contract.query.order_by(Contract.created_at.desc()).all()

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>العقود - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    <a class="nav-link" href="{{ url_for('logout') }}">خروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-file-contract"></i> العقود</h2>
                <a href="{{ url_for('add_contract') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة عقد جديد
                </a>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>رقم العقد</th>
                                    <th>نوع العقد</th>
                                    <th>طريقة الدفع</th>
                                    <th>العميل</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for contract in contracts %}
                                <tr>
                                    <td>{{ contract.contract_number }}</td>
                                    <td>
                                        {% if contract.contract_type == 'car_sale' %}
                                            <span class="badge bg-primary">بيع سيارة</span>
                                        {% elif contract.contract_type == 'premium_number_sale' %}
                                            <span class="badge bg-success">بيع رقم مميز</span>
                                        {% elif contract.contract_type == 'combined_sale' %}
                                            <span class="badge bg-info">عقد مدمج</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if contract.payment_type == 'cash' %}
                                            نقدي
                                        {% elif contract.payment_type == 'installment' %}
                                            تقسيط
                                        {% elif contract.payment_type == 'lease' %}
                                            إيجار
                                        {% elif contract.payment_type == 'trade_in' %}
                                            استبدال
                                        {% endif %}
                                    </td>
                                    <td>{{ contract.customer.name_ar }}</td>
                                    <td>{{ "{:,.0f}".format(contract.total_amount) }} ر.ق</td>
                                    <td>
                                        {% if contract.status == 'draft' %}
                                            <span class="badge bg-secondary">مسودة</span>
                                        {% elif contract.status == 'active' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% elif contract.status == 'completed' %}
                                            <span class="badge bg-primary">مكتمل</span>
                                        {% elif contract.status == 'cancelled' %}
                                            <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ contract.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('view_contract', contract_id=contract.id) }}" class="btn btn-sm btn-info" title="عرض العقد">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('edit_contract', contract_id=contract.id) }}" class="btn btn-sm btn-warning" title="تعديل العقد">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('generate_contract_pdf', contract_id=contract.id) }}" class="btn btn-sm btn-success" title="طباعة العقد" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <button class="btn btn-sm btn-danger" onclick="deleteContract({{ contract.id }})" title="حذف العقد">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function deleteContract(contractId) {
                if (confirm('هل أنت متأكد من حذف هذا العقد؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                    fetch('/contracts/delete/' + contractId, {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('تم حذف العقد بنجاح');
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('حدث خطأ في الاتصال');
                    });
                }
            }
        </script>
    </body>
    </html>
    ''', contracts=contracts_list)

# ==================== إدارة السيارات ====================
@app.route('/cars')
def cars():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    cars_list = Car.query.all()
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إدارة السيارات - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    <a class="nav-link" href="{{ url_for('cars') }}">السيارات</a>
                    <a class="nav-link" href="{{ url_for('premium_numbers') }}">الأرقام المميزة</a>
                    <a class="nav-link" href="{{ url_for('customers') }}">العملاء</a>
                    <a class="nav-link" href="{{ url_for('contracts') }}">العقود</a>
                    <a class="nav-link" href="{{ url_for('logout') }}">خروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-car"></i> إدارة السيارات</h2>
                <a href="{{ url_for('add_car') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة سيارة جديدة
                </a>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الماركة والموديل</th>
                                    <th>السنة</th>
                                    <th>اللون</th>
                                    <th>المسافة</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for car in cars %}
                                <tr>
                                    <td>
                                        <strong>{{ car.make }} {{ car.model }}</strong><br>
                                        <small class="text-muted">{{ car.body_type or 'غير محدد' }}</small>
                                    </td>
                                    <td>{{ car.year }}</td>
                                    <td>{{ car.color or 'غير محدد' }}</td>
                                    <td>{{ "{:,}".format(car.mileage) + " كم" if car.mileage else 'غير محدد' }}</td>
                                    <td>
                                        <strong class="text-success">{{ "{:,.0f}".format(car.price) }} ر.ق</strong>
                                        {% if car.purchase_price %}
                                            <br><small class="text-muted">شراء: {{ "{:,.0f}".format(car.purchase_price) }} ر.ق</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if car.status == 'available' %}
                                            <span class="badge bg-success">متاح</span>
                                        {% elif car.status == 'sold' %}
                                            <span class="badge bg-danger">مباع</span>
                                        {% elif car.status == 'reserved' %}
                                            <span class="badge bg-warning">محجوز</span>
                                        {% elif car.status == 'maintenance' %}
                                            <span class="badge bg-info">صيانة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('view_car', car_id=car.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                        <a href="{{ url_for('edit_car', car_id=car.id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <a href="{{ url_for('delete_car', car_id=car.id) }}" class="btn btn-sm btn-danger"
                                           onclick="return confirm('هل أنت متأكد من حذف هذه السيارة؟')">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', cars=cars_list)

@app.route('/cars/add', methods=['GET', 'POST'])
def add_car():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    if request.method == 'POST':
        try:
            from datetime import datetime

            # تحويل التواريخ
            insurance_expiry = None
            registration_expiry = None
            purchase_date = None

            if request.form.get('insurance_expiry'):
                insurance_expiry = datetime.strptime(request.form['insurance_expiry'], '%Y-%m-%d').date()
            if request.form.get('registration_expiry'):
                registration_expiry = datetime.strptime(request.form['registration_expiry'], '%Y-%m-%d').date()
            if request.form.get('purchase_date'):
                purchase_date = datetime.strptime(request.form['purchase_date'], '%Y-%m-%d').date()

            car = Car(
                make=request.form['make'],
                model=request.form['model'],
                year=int(request.form['year']),
                price=float(request.form['price']),
                status='available',
                color=request.form.get('color'),
                fuel_type=request.form.get('fuel_type'),
                transmission=request.form.get('transmission'),
                engine_size=request.form.get('engine_size'),
                mileage=int(request.form['mileage']) if request.form.get('mileage') else None,
                body_type=request.form.get('body_type'),
                doors=int(request.form['doors']) if request.form.get('doors') else None,
                seats=int(request.form['seats']) if request.form.get('seats') else None,
                vin_number=request.form.get('vin_number'),
                license_plate=request.form.get('license_plate'),
                insurance_expiry=insurance_expiry,
                registration_expiry=registration_expiry,
                condition=request.form.get('condition'),
                features=request.form.get('features'),
                notes=request.form.get('notes'),
                purchase_date=purchase_date,
                purchase_price=float(request.form['purchase_price']) if request.form.get('purchase_price') else None
            )
            db.session.add(car)
            db.session.commit()
            flash('تم إضافة السيارة بنجاح مع جميع البيانات', 'success')
            return redirect(url_for('cars'))
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إضافة سيارة جديدة - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('cars') }}">السيارات</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <h2><i class="fas fa-plus"></i> إضافة سيارة جديدة</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <!-- المعلومات الأساسية -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-info-circle"></i> المعلومات الأساسية</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الماركة *</label>
                                            <select class="form-select" name="make" required>
                                                <option value="">اختر الماركة</option>
                                                <option value="تويوتا">تويوتا</option>
                                                <option value="نيسان">نيسان</option>
                                                <option value="هوندا">هوندا</option>
                                                <option value="لكزس">لكزس</option>
                                                <option value="مرسيدس">مرسيدس</option>
                                                <option value="بي إم دبليو">بي إم دبليو</option>
                                                <option value="أودي">أودي</option>
                                                <option value="فولكس واجن">فولكس واجن</option>
                                                <option value="هيونداي">هيونداي</option>
                                                <option value="كيا">كيا</option>
                                                <option value="فورد">فورد</option>
                                                <option value="شيفروليه">شيفروليه</option>
                                                <option value="جيب">جيب</option>
                                                <option value="لاند روفر">لاند روفر</option>
                                                <option value="بورش">بورش</option>
                                                <option value="أخرى">أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الموديل *</label>
                                            <input type="text" class="form-control" name="model" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">السنة *</label>
                                            <input type="number" class="form-control" name="year" min="1990" max="2030" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">اللون</label>
                                            <select class="form-select" name="color">
                                                <option value="">اختر اللون</option>
                                                <option value="أبيض">أبيض</option>
                                                <option value="أسود">أسود</option>
                                                <option value="فضي">فضي</option>
                                                <option value="رمادي">رمادي</option>
                                                <option value="أحمر">أحمر</option>
                                                <option value="أزرق">أزرق</option>
                                                <option value="أخضر">أخضر</option>
                                                <option value="بني">بني</option>
                                                <option value="ذهبي">ذهبي</option>
                                                <option value="برتقالي">برتقالي</option>
                                                <option value="أصفر">أصفر</option>
                                                <option value="بنفسجي">بنفسجي</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">نوع الوقود</label>
                                            <select class="form-select" name="fuel_type">
                                                <option value="">اختر نوع الوقود</option>
                                                <option value="بنزين">بنزين</option>
                                                <option value="ديزل">ديزل</option>
                                                <option value="هجين">هجين</option>
                                                <option value="كهربائي">كهربائي</option>
                                                <option value="غاز طبيعي">غاز طبيعي</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">ناقل الحركة</label>
                                            <select class="form-select" name="transmission">
                                                <option value="">اختر ناقل الحركة</option>
                                                <option value="أوتوماتيك">أوتوماتيك</option>
                                                <option value="يدوي">يدوي</option>
                                                <option value="CVT">CVT</option>
                                                <option value="نصف أوتوماتيك">نصف أوتوماتيك</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- المواصفات التقنية -->
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5><i class="fas fa-cogs"></i> المواصفات التقنية</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">حجم المحرك</label>
                                            <input type="text" class="form-control" name="engine_size" placeholder="مثال: 2.0L">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">المسافة المقطوعة (كم)</label>
                                            <input type="number" class="form-control" name="mileage" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">عدد الأبواب</label>
                                            <select class="form-select" name="doors">
                                                <option value="">اختر عدد الأبواب</option>
                                                <option value="2">2 أبواب</option>
                                                <option value="3">3 أبواب</option>
                                                <option value="4">4 أبواب</option>
                                                <option value="5">5 أبواب</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">عدد المقاعد</label>
                                            <select class="form-select" name="seats">
                                                <option value="">اختر عدد المقاعد</option>
                                                <option value="2">2 مقاعد</option>
                                                <option value="4">4 مقاعد</option>
                                                <option value="5">5 مقاعد</option>
                                                <option value="7">7 مقاعد</option>
                                                <option value="8">8 مقاعد</option>
                                                <option value="9">9 مقاعد</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع الهيكل</label>
                                            <select class="form-select" name="body_type">
                                                <option value="">اختر نوع الهيكل</option>
                                                <option value="سيدان">سيدان</option>
                                                <option value="هاتشباك">هاتشباك</option>
                                                <option value="SUV">SUV</option>
                                                <option value="كوبيه">كوبيه</option>
                                                <option value="كونفرتيبل">كونفرتيبل</option>
                                                <option value="ستيشن واجن">ستيشن واجن</option>
                                                <option value="بيك أب">بيك أب</option>
                                                <option value="فان">فان</option>
                                                <option value="كروس أوفر">كروس أوفر</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">حالة السيارة</label>
                                            <select class="form-select" name="condition">
                                                <option value="">اختر الحالة</option>
                                                <option value="ممتازة">ممتازة</option>
                                                <option value="جيدة جداً">جيدة جداً</option>
                                                <option value="جيدة">جيدة</option>
                                                <option value="مقبولة">مقبولة</option>
                                                <option value="تحتاج صيانة">تحتاج صيانة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- المعلومات القانونية -->
                        <div class="card mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h5><i class="fas fa-file-alt"></i> المعلومات القانونية</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الشاسيه (VIN)</label>
                                            <input type="text" class="form-control" name="vin_number" placeholder="17 رقم/حرف">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم اللوحة</label>
                                            <input type="text" class="form-control" name="license_plate">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">انتهاء التأمين</label>
                                            <input type="date" class="form-control" name="insurance_expiry">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">انتهاء الترخيص</label>
                                            <input type="date" class="form-control" name="registration_expiry">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- المعلومات المالية -->
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h5><i class="fas fa-money-bill-wave"></i> المعلومات المالية</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">سعر البيع (ر.ق) *</label>
                                            <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">سعر الشراء (ر.ق)</label>
                                            <input type="number" class="form-control" name="purchase_price" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">تاريخ الشراء</label>
                                            <input type="date" class="form-control" name="purchase_date">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- المميزات والملاحظات -->
                        <div class="card mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h5><i class="fas fa-star"></i> المميزات والملاحظات</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المميزات الإضافية</label>
                                            <textarea class="form-control" name="features" rows="4"
                                                placeholder="مثال: مكيف، نظام ملاحة، كاميرا خلفية، مقاعد جلدية..."></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">ملاحظات</label>
                                            <textarea class="form-control" name="notes" rows="4"
                                                placeholder="أي ملاحظات إضافية حول السيارة..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ السيارة
                            </button>
                            <a href="{{ url_for('cars') }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/cars/edit/<int:car_id>', methods=['GET', 'POST'])
def edit_car(car_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    car = Car.query.get_or_404(car_id)

    if request.method == 'POST':
        try:
            from datetime import datetime

            # تحديث البيانات الأساسية
            car.make = request.form['make']
            car.model = request.form['model']
            car.year = int(request.form['year'])
            car.price = float(request.form['price'])
            car.status = request.form['status']

            # تحديث البيانات الإضافية
            car.color = request.form.get('color')
            car.fuel_type = request.form.get('fuel_type')
            car.transmission = request.form.get('transmission')
            car.engine_size = request.form.get('engine_size')
            car.mileage = int(request.form['mileage']) if request.form.get('mileage') else None
            car.body_type = request.form.get('body_type')
            car.doors = int(request.form['doors']) if request.form.get('doors') else None
            car.seats = int(request.form['seats']) if request.form.get('seats') else None
            car.vin_number = request.form.get('vin_number')
            car.license_plate = request.form.get('license_plate')
            car.condition = request.form.get('condition')
            car.features = request.form.get('features')
            car.notes = request.form.get('notes')
            car.purchase_price = float(request.form['purchase_price']) if request.form.get('purchase_price') else None

            # تحديث التواريخ
            if request.form.get('insurance_expiry'):
                car.insurance_expiry = datetime.strptime(request.form['insurance_expiry'], '%Y-%m-%d').date()
            if request.form.get('registration_expiry'):
                car.registration_expiry = datetime.strptime(request.form['registration_expiry'], '%Y-%m-%d').date()
            if request.form.get('purchase_date'):
                car.purchase_date = datetime.strptime(request.form['purchase_date'], '%Y-%m-%d').date()

            db.session.commit()
            flash('تم تحديث السيارة بنجاح مع جميع البيانات', 'success')
            return redirect(url_for('cars'))
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تعديل السيارة - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('cars') }}">السيارات</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <h2><i class="fas fa-edit"></i> تعديل السيارة</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الماركة</label>
                                    <input type="text" class="form-control" name="make" value="{{ car.make }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الموديل</label>
                                    <input type="text" class="form-control" name="model" value="{{ car.model }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">السنة</label>
                                    <input type="number" class="form-control" name="year" value="{{ car.year }}" min="1990" max="2030" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">السعر (ر.ق)</label>
                                    <input type="number" class="form-control" name="price" value="{{ car.price }}" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-select" name="status" required>
                                        <option value="available" {{ 'selected' if car.status == 'available' else '' }}>متاح</option>
                                        <option value="sold" {{ 'selected' if car.status == 'sold' else '' }}>مباع</option>
                                        <option value="reserved" {{ 'selected' if car.status == 'reserved' else '' }}>محجوز</option>
                                        <option value="maintenance" {{ 'selected' if car.status == 'maintenance' else '' }}>صيانة</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{{ url_for('cars') }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', car=car)

@app.route('/cars/delete/<int:car_id>')
def delete_car(car_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        car = Car.query.get_or_404(car_id)

        # Check if car has contracts
        if car.contracts:
            flash('لا يمكن حذف السيارة لأنها مرتبطة بعقود موجودة', 'error')
        else:
            db.session.delete(car)
            db.session.commit()
            flash('تم حذف السيارة بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')

    return redirect(url_for('cars'))

@app.route('/cars/view/<int:car_id>')
def view_car(car_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    car = Car.query.get_or_404(car_id)

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تفاصيل السيارة - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .car-detail-card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .status-badge { font-size: 1.1em; }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('cars') }}">السيارات</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-car"></i> تفاصيل السيارة</h2>
                <div>
                    <a href="{{ url_for('edit_car', car_id=car.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                    <a href="{{ url_for('cars') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card car-detail-card">
                        <div class="card-header bg-primary text-white">
                            <h4><i class="fas fa-info-circle"></i> معلومات السيارة</h4>
                        </div>
                        <div class="card-body">
                            <!-- المعلومات الأساسية -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <h6><i class="fas fa-tag"></i> الماركة</h6>
                                    <p class="lead">{{ car.make }}</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-car-side"></i> الموديل</h6>
                                    <p class="lead">{{ car.model }}</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-calendar"></i> السنة</h6>
                                    <p class="lead">{{ car.year }}</p>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <h6><i class="fas fa-palette"></i> اللون</h6>
                                    <p>{{ car.color or 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-gas-pump"></i> نوع الوقود</h6>
                                    <p>{{ car.fuel_type or 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-cogs"></i> ناقل الحركة</h6>
                                    <p>{{ car.transmission or 'غير محدد' }}</p>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <h6><i class="fas fa-tachometer-alt"></i> حجم المحرك</h6>
                                    <p>{{ car.engine_size or 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-road"></i> المسافة المقطوعة</h6>
                                    <p>{{ "{:,}".format(car.mileage) + " كم" if car.mileage else 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-car"></i> نوع الهيكل</h6>
                                    <p>{{ car.body_type or 'غير محدد' }}</p>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <h6><i class="fas fa-door-open"></i> عدد الأبواب</h6>
                                    <p>{{ car.doors or 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-chair"></i> عدد المقاعد</h6>
                                    <p>{{ car.seats or 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-star"></i> الحالة</h6>
                                    <p>{{ car.condition or 'غير محدد' }}</p>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-barcode"></i> رقم الشاسيه</h6>
                                    <p>{{ car.vin_number or 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-id-card"></i> رقم اللوحة</h6>
                                    <p>{{ car.license_plate or 'غير محدد' }}</p>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-shield-alt"></i> انتهاء التأمين</h6>
                                    <p>{{ car.insurance_expiry.strftime('%Y-%m-%d') if car.insurance_expiry else 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-file-alt"></i> انتهاء الترخيص</h6>
                                    <p>{{ car.registration_expiry.strftime('%Y-%m-%d') if car.registration_expiry else 'غير محدد' }}</p>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <h6><i class="fas fa-money-bill"></i> سعر البيع</h6>
                                    <p class="lead text-success">{{ "{:,.0f}".format(car.price) }} ر.ق</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-shopping-cart"></i> سعر الشراء</h6>
                                    <p>{{ "{:,.0f}".format(car.purchase_price) + " ر.ق" if car.purchase_price else 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-calendar-plus"></i> تاريخ الشراء</h6>
                                    <p>{{ car.purchase_date.strftime('%Y-%m-%d') if car.purchase_date else 'غير محدد' }}</p>
                                </div>
                            </div>

                            {% if car.features %}
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h6><i class="fas fa-star"></i> المميزات الإضافية</h6>
                                    <p>{{ car.features }}</p>
                                </div>
                            </div>
                            {% endif %}

                            {% if car.notes %}
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h6><i class="fas fa-sticky-note"></i> ملاحظات</h6>
                                    <p>{{ car.notes }}</p>
                                </div>
                            </div>
                            {% endif %}

                            <div class="row">
                                <div class="col-md-12">
                                    <h6><i class="fas fa-flag"></i> حالة السيارة</h6>
                                    {% if car.status == 'available' %}
                                        <span class="badge bg-success status-badge">متاح</span>
                                    {% elif car.status == 'sold' %}
                                        <span class="badge bg-danger status-badge">مباع</span>
                                    {% elif car.status == 'reserved' %}
                                        <span class="badge bg-warning status-badge">محجوز</span>
                                    {% elif car.status == 'maintenance' %}
                                        <span class="badge bg-info status-badge">صيانة</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card car-detail-card">
                        <div class="card-header bg-info text-white">
                            <h5><i class="fas fa-file-contract"></i> العقود المرتبطة</h5>
                        </div>
                        <div class="card-body">
                            {% if car.contracts %}
                                {% for contract in car.contracts %}
                                <div class="mb-2">
                                    <strong>{{ contract.contract_number }}</strong><br>
                                    <small class="text-muted">{{ contract.customer.name_ar }}</small><br>
                                    <small class="text-success">{{ "{:,.0f}".format(contract.total_amount) }} ر.ق</small>
                                </div>
                                <hr>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted">لا توجد عقود مرتبطة بهذه السيارة</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="card car-detail-card mt-3">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
                        </div>
                        <div class="card-body">
                            {% if car.status == 'available' %}
                                <a href="{{ url_for('add_contract') }}?car_id={{ car.id }}" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-file-contract"></i> إنشاء عقد بيع
                                </a>
                            {% endif %}
                            <a href="{{ url_for('edit_car', car_id=car.id) }}" class="btn btn-warning w-100 mb-2">
                                <i class="fas fa-edit"></i> تعديل البيانات
                            </a>
                            {% if not car.contracts %}
                                <a href="{{ url_for('delete_car', car_id=car.id) }}" class="btn btn-danger w-100"
                                   onclick="return confirm('هل أنت متأكد من حذف هذه السيارة؟')">
                                    <i class="fas fa-trash"></i> حذف السيارة
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', car=car)

# ==================== إدارة الأرقام المميزة ====================
@app.route('/premium_numbers')
def premium_numbers():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # Get numbers with auction information
    numbers_list = PremiumNumber.query.all()

    # Get auction statistics
    total_numbers = PremiumNumber.query.count()
    available_numbers = PremiumNumber.query.filter_by(status='available').count()
    auction_numbers = PremiumNumber.query.filter_by(status='auction').count()
    sold_numbers = PremiumNumber.query.filter_by(status='sold').count()

    # Get active auctions for numbers
    active_auctions = Auction.query.filter_by(status='active').all()
    auction_dict = {auction.premium_number_id: auction for auction in active_auctions}

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إدارة الأرقام المميزة - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .stat-card {
                transition: transform 0.3s;
                border-radius: 15px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .stat-card:hover {
                transform: translateY(-5px);
            }
            .auction-badge {
                position: relative;
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
                100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
            }
            .number-highlight {
                font-size: 1.2em;
                font-weight: bold;
                color: #007bff;
            }
            .auction-info {
                background: linear-gradient(135deg, #ffc107, #ff8c00);
                color: white;
                border-radius: 10px;
                padding: 10px;
                margin: 5px 0;
            }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    <a class="nav-link" href="{{ url_for('cars') }}">السيارات</a>
                    <a class="nav-link" href="{{ url_for('premium_numbers') }}">الأرقام المميزة</a>
                    <a class="nav-link" href="{{ url_for('customers') }}">العملاء</a>
                    <a class="nav-link" href="{{ url_for('contracts') }}">العقود</a>
                    <a class="nav-link" href="{{ url_for('auction') }}">المزاد</a>
                    <a class="nav-link" href="{{ url_for('logout') }}">خروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-hashtag"></i> إدارة الأرقام المميزة</h2>
                <div>
                    <a href="{{ url_for('add_premium_number') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة رقم مميز
                    </a>
                    <a href="{{ url_for('auction_management') }}" class="btn btn-warning">
                        <i class="fas fa-gavel"></i> إدارة المزادات
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card stat-card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-hashtag fa-2x mb-2"></i>
                            <h4>{{ total_numbers }}</h4>
                            <p>إجمالي الأرقام</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h4>{{ available_numbers }}</h4>
                            <p>متاح للبيع</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-gavel fa-2x mb-2"></i>
                            <h4>{{ auction_numbers }}</h4>
                            <p>في المزاد</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-handshake fa-2x mb-2"></i>
                            <h4>{{ sold_numbers }}</h4>
                            <p>مباع</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6><i class="fas fa-bolt"></i> إجراءات سريعة</h6>
                            <p class="mb-0 text-muted">إنشاء مزادات متعددة أو إدارة الأرقام بشكل جماعي</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-warning" onclick="showBulkAuctionModal()" disabled>
                                <i class="fas fa-gavel"></i> مزادات متعددة
                            </button>
                            <button class="btn btn-danger" onclick="showBulkDeleteModal()" disabled>
                                <i class="fas fa-trash"></i> حذف متعدد
                            </button>
                            <a href="{{ url_for('create_auction') }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> مزاد جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Numbers Table -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> قائمة الأرقام المميزة</h5>
                    <div>
                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        <label for="selectAll" class="text-white ms-2">تحديد الكل</label>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="selectAllTable" onchange="toggleSelectAllTable()">
                                    </th>
                                    <th>الرقم</th>
                                    <th>الفئة</th>
                                    <th>السعر الأساسي</th>
                                    <th>الحالة</th>
                                    <th>معلومات المزاد</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for number in numbers %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="number-checkbox" value="{{ number.id }}" data-number="{{ number.number }}">
                                    </td>
                                    <td>
                                        <span class="number-highlight">{{ number.number }}</span>
                                    </td>
                                    <td>
                                        {% if number.category == 'VIP' %}
                                            <span class="badge bg-warning text-dark">VIP</span>
                                        {% elif number.category == 'مميز' %}
                                            <span class="badge bg-success">مميز</span>
                                        {% elif number.category == 'خاص' %}
                                            <span class="badge bg-purple text-white">خاص</span>
                                        {% else %}
                                            <span class="badge bg-info">{{ number.category }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ "{:,.0f}".format(number.price) }} ر.ق</strong>
                                    </td>
                                    <td>
                                        {% if number.status == 'available' %}
                                            <span class="badge bg-success">متاح</span>
                                        {% elif number.status == 'sold' %}
                                            <span class="badge bg-danger">مباع</span>
                                        {% elif number.status == 'auction' %}
                                            <span class="badge bg-warning auction-badge">في المزاد</span>
                                        {% elif number.status == 'reserved' %}
                                            <span class="badge bg-secondary">محجوز</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if number.id in auction_dict %}
                                            {% set auction = auction_dict[number.id] %}
                                            <div class="auction-info">
                                                <small>
                                                    <i class="fas fa-gavel"></i> مزاد نشط<br>
                                                    <strong>{{ "{:,.0f}".format(auction.current_price if auction.current_price > 0 else auction.starting_price) }} ر.ق</strong><br>
                                                    <i class="fas fa-users"></i> {{ auction.total_bids }} مزايدة<br>
                                                    <i class="fas fa-clock"></i> ينتهي: {{ auction.end_time.strftime('%m-%d %H:%M') }}
                                                </small>
                                            </div>
                                        {% elif number.status == 'available' %}
                                            <span class="text-muted">جاهز للمزاد</span>
                                        {% elif number.status == 'sold' %}
                                            <span class="text-success">تم البيع</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            {% if number.status == 'available' %}
                                                <a href="{{ url_for('create_auction_for_number', number_id=number.id) }}"
                                                   class="btn btn-sm btn-success" title="إنشاء مزاد للرقم {{ number.number }}">
                                                    <i class="fas fa-gavel"></i> مزاد
                                                </a>
                                            {% elif number.status == 'auction' and number.id in auction_dict %}
                                                <a href="{{ url_for('auction_details', auction_id=auction_dict[number.id].id) }}"
                                                   class="btn btn-sm btn-warning" title="عرض المزاد النشط">
                                                    <i class="fas fa-eye"></i> المزاد
                                                </a>
                                            {% endif %}

                                            <a href="{{ url_for('view_premium_number', number_id=number.id) }}"
                                               class="btn btn-sm btn-info" title="عرض تفاصيل الرقم {{ number.number }}">
                                                <i class="fas fa-info-circle"></i>
                                            </a>

                                            <a href="{{ url_for('edit_premium_number', number_id=number.id) }}"
                                               class="btn btn-sm btn-primary" title="تعديل الرقم {{ number.number }}">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <a href="{{ url_for('delete_premium_number', number_id=number.id) }}"
                                               class="btn btn-sm btn-danger" title="حذف الرقم {{ number.number }}"
                                               onclick="return confirm('{% if number.status == 'auction' %}تحذير: هذا الرقم في مزاد نشط! {% elif number.status == 'sold' %}تحذير: هذا الرقم مباع! {% endif %}هل أنت متأكد من حذف الرقم {{ number.number }}؟ سيتم حذف جميع البيانات المرتبطة به.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- Bulk Auction Modal -->
        <div class="modal fade" id="bulkAuctionModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إنشاء مزادات متعددة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="selectedNumbers"></div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">مدة المزاد</label>
                                <select class="form-select" id="bulkDuration" onchange="checkBulkFastAuction(this.value)">
                                    <option value="2">دقيقتان (سريع جداً)</option>
                                    <option value="5">5 دقائق (سريع)</option>
                                    <option value="10">10 دقائق</option>
                                    <option value="15">15 دقيقة</option>
                                    <option value="30">30 دقيقة (نصف ساعة)</option>
                                    <option value="60">ساعة واحدة</option>
                                    <option value="120">ساعتان</option>
                                    <option value="180">3 ساعات</option>
                                    <option value="360">6 ساعات</option>
                                    <option value="720">12 ساعة</option>
                                    <option value="1440" selected>24 ساعة (يوم)</option>
                                    <option value="2880">48 ساعة (يومان)</option>
                                    <option value="4320">72 ساعة (3 أيام)</option>
                                </select>
                                <div id="bulkFastAuctionWarning" class="alert alert-warning mt-2" style="display: none;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>تحذير:</strong> المزادات السريعة (أقل من 15 دقيقة) مناسبة للاختبار أو العروض الخاصة فقط!
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الحد الأدنى للزيادة</label>
                                <input type="number" class="form-control" id="bulkIncrement" value="1000" step="100">
                            </div>
                        </div>
                        <div class="form-check mt-3">
                            <input class="form-check-input" type="checkbox" id="bulkAutoExtend" checked>
                            <label class="form-check-label" for="bulkAutoExtend">
                                تمديد تلقائي للمزادات
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="createBulkAuctions()">
                            <i class="fas fa-gavel"></i> إنشاء المزادات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Delete Modal -->
        <div class="modal fade" id="bulkDeleteModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">حذف أرقام متعددة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>تحذير:</strong> سيتم حذف الأرقام المحددة وجميع البيانات المرتبطة بها (المزادات، العقود، المزايدات). هذا الإجراء لا يمكن التراجع عنه!
                        </div>
                        <div id="selectedNumbersDelete"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" onclick="confirmBulkDelete()">
                            <i class="fas fa-trash"></i> تأكيد الحذف
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function toggleSelectAll() {
                const selectAll = document.getElementById('selectAll');
                const checkboxes = document.querySelectorAll('.number-checkbox');
                checkboxes.forEach(cb => cb.checked = selectAll.checked);
                updateSelectedCount();
            }

            function toggleSelectAllTable() {
                const selectAllTable = document.getElementById('selectAllTable');
                const checkboxes = document.querySelectorAll('.number-checkbox');
                checkboxes.forEach(cb => cb.checked = selectAllTable.checked);
                updateSelectedCount();
            }

            function updateSelectedCount() {
                const selected = document.querySelectorAll('.number-checkbox:checked');
                const bulkButton = document.querySelector('button[onclick="showBulkAuctionModal()"]');
                const deleteButton = document.querySelector('button[onclick="showBulkDeleteModal()"]');

                if (selected.length > 0) {
                    bulkButton.innerHTML = `<i class="fas fa-gavel"></i> مزادات متعددة (${selected.length})`;
                    bulkButton.disabled = false;
                    deleteButton.innerHTML = `<i class="fas fa-trash"></i> حذف متعدد (${selected.length})`;
                    deleteButton.disabled = false;
                } else {
                    bulkButton.innerHTML = '<i class="fas fa-gavel"></i> مزادات متعددة';
                    bulkButton.disabled = true;
                    deleteButton.innerHTML = '<i class="fas fa-trash"></i> حذف متعدد';
                    deleteButton.disabled = true;
                }
            }

            function showBulkAuctionModal() {
                const selected = document.querySelectorAll('.number-checkbox:checked');
                if (selected.length === 0) {
                    alert('يرجى اختيار رقم واحد على الأقل');
                    return;
                }

                let html = '<h6>الأرقام المحددة:</h6><ul>';
                selected.forEach(cb => {
                    html += `<li>الرقم المميز: <strong>${cb.dataset.number}</strong></li>`;
                });
                html += '</ul>';

                document.getElementById('selectedNumbers').innerHTML = html;
                new bootstrap.Modal(document.getElementById('bulkAuctionModal')).show();
            }

            // Check for bulk fast auction warning
            function checkBulkFastAuction(duration) {
                const warningDiv = document.getElementById('bulkFastAuctionWarning');
                if (parseInt(duration) < 15) {
                    warningDiv.style.display = 'block';
                } else {
                    warningDiv.style.display = 'none';
                }
            }

            function createBulkAuctions() {
                const selected = document.querySelectorAll('.number-checkbox:checked');
                const numberIds = Array.from(selected).map(cb => parseInt(cb.value));

                const settings = {
                    duration_minutes: parseInt(document.getElementById('bulkDuration').value),
                    bid_increment: parseFloat(document.getElementById('bulkIncrement').value),
                    auto_extend: document.getElementById('bulkAutoExtend').checked
                };

                fetch('/premium_numbers/bulk_auction', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        number_ids: numberIds,
                        settings: settings
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`تم إنشاء ${data.count} مزاد بنجاح!`);
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                });
            }

            // Update count on checkbox change
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('number-checkbox')) {
                    updateSelectedCount();
                }
            });

            function showBulkDeleteModal() {
                const selected = document.querySelectorAll('.number-checkbox:checked');
                if (selected.length === 0) {
                    alert('يرجى اختيار رقم واحد على الأقل');
                    return;
                }

                let html = '<h6>الأرقام المحددة للحذف:</h6><ul class="list-group">';
                selected.forEach(cb => {
                    html += `<li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>الرقم المميز: <strong>${cb.dataset.number}</strong></span>
                        <span class="badge bg-danger">سيتم حذفه</span>
                    </li>`;
                });
                html += '</ul>';

                document.getElementById('selectedNumbersDelete').innerHTML = html;
                new bootstrap.Modal(document.getElementById('bulkDeleteModal')).show();
            }

            function confirmBulkDelete() {
                const selected = document.querySelectorAll('.number-checkbox:checked');
                const numberIds = Array.from(selected).map(cb => parseInt(cb.value));

                if (!confirm(`هل أنت متأكد من حذف ${numberIds.length} رقم مميز؟ سيتم حذف جميع البيانات المرتبطة بها!`)) {
                    return;
                }

                fetch('/premium_numbers/bulk_delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        number_ids: numberIds
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`تم حذف ${data.count} رقم مميز بنجاح!`);
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                });
            }

            // Initialize
            updateSelectedCount();
        </script>
    </body>
    </html>
    ''', numbers=numbers_list,
         total_numbers=total_numbers,
         available_numbers=available_numbers,
         auction_numbers=auction_numbers,
         sold_numbers=sold_numbers,
         auction_dict=auction_dict)

@app.route('/premium_numbers/add', methods=['GET', 'POST'])
def add_premium_number():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    if request.method == 'POST':
        try:
            number = PremiumNumber(
                number=request.form['number'],
                category=request.form['category'],
                price=float(request.form['price']),
                status='available'
            )
            db.session.add(number)
            db.session.commit()
            flash('تم إضافة الرقم المميز بنجاح', 'success')
            return redirect(url_for('premium_numbers'))
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إضافة رقم مميز جديد - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('premium_numbers') }}">الأرقام المميزة</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <h2><i class="fas fa-plus"></i> إضافة رقم مميز جديد</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرقم</label>
                                    <input type="text" class="form-control" name="number" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الفئة</label>
                                    <select class="form-select" name="category" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="VIP">VIP</option>
                                        <option value="مميز">مميز</option>
                                        <option value="عادي">عادي</option>
                                        <option value="خاص">خاص</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">السعر (ر.ق)</label>
                                    <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ الرقم المميز
                            </button>
                            <a href="{{ url_for('premium_numbers') }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/premium_numbers/edit/<int:number_id>', methods=['GET', 'POST'])
def edit_premium_number(number_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    number = PremiumNumber.query.get_or_404(number_id)

    if request.method == 'POST':
        try:
            number.number = request.form['number']
            number.category = request.form['category']
            number.price = float(request.form['price'])
            number.status = request.form['status']

            db.session.commit()
            flash('تم تحديث الرقم المميز بنجاح', 'success')
            return redirect(url_for('premium_numbers'))
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تعديل الرقم المميز - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('premium_numbers') }}">الأرقام المميزة</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <h2><i class="fas fa-edit"></i> تعديل الرقم المميز</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرقم</label>
                                    <input type="text" class="form-control" name="number" value="{{ number.number }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الفئة</label>
                                    <select class="form-select" name="category" required>
                                        <option value="VIP" {{ 'selected' if number.category == 'VIP' else '' }}>VIP</option>
                                        <option value="مميز" {{ 'selected' if number.category == 'مميز' else '' }}>مميز</option>
                                        <option value="عادي" {{ 'selected' if number.category == 'عادي' else '' }}>عادي</option>
                                        <option value="خاص" {{ 'selected' if number.category == 'خاص' else '' }}>خاص</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">السعر (ر.ق)</label>
                                    <input type="number" class="form-control" name="price" value="{{ number.price }}" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-select" name="status" required>
                                        <option value="available" {{ 'selected' if number.status == 'available' else '' }}>متاح</option>
                                        <option value="sold" {{ 'selected' if number.status == 'sold' else '' }}>مباع</option>
                                        <option value="auction" {{ 'selected' if number.status == 'auction' else '' }}>في المزاد</option>
                                        <option value="reserved" {{ 'selected' if number.status == 'reserved' else '' }}>محجوز</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{{ url_for('premium_numbers') }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', number=number)

@app.route('/premium_numbers/delete/<int:number_id>')
def delete_premium_number(number_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        number = PremiumNumber.query.get_or_404(number_id)

        # Store number for message
        number_text = number.number

        # Delete related auctions first
        auctions = Auction.query.filter_by(premium_number_id=number_id).all()
        for auction in auctions:
            # Delete auction bids
            Bid.query.filter_by(auction_id=auction.id).delete()
            # Delete auction activities
            AuctionActivity.query.filter_by(auction_id=auction.id).delete()
            # Delete auction watches
            AuctionWatch.query.filter_by(auction_id=auction.id).delete()
            # Delete auction
            db.session.delete(auction)

        # Delete related contracts
        contracts = Contract.query.filter_by(premium_number_id=number_id).all()
        for contract in contracts:
            db.session.delete(contract)

        # Delete the number itself
        db.session.delete(number)
        db.session.commit()

        flash(f'تم حذف الرقم المميز {number_text} وجميع البيانات المرتبطة به بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')

    return redirect(url_for('premium_numbers'))

@app.route('/premium_numbers/bulk_delete', methods=['POST'])
def bulk_delete_premium_numbers():
    """حذف أرقام مميزة متعددة"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})

    try:
        data = request.get_json()
        number_ids = data.get('number_ids', [])

        if not number_ids:
            return jsonify({'success': False, 'message': 'لم يتم تحديد أي أرقام'})

        deleted_count = 0

        for number_id in number_ids:
            try:
                number = PremiumNumber.query.get(number_id)
                if number:
                    # Delete related auctions first
                    auctions = Auction.query.filter_by(premium_number_id=number_id).all()
                    for auction in auctions:
                        # Delete auction bids
                        Bid.query.filter_by(auction_id=auction.id).delete()
                        # Delete auction activities
                        AuctionActivity.query.filter_by(auction_id=auction.id).delete()
                        # Delete auction watches
                        AuctionWatch.query.filter_by(auction_id=auction.id).delete()
                        # Delete auction
                        db.session.delete(auction)

                    # Delete related contracts
                    contracts = Contract.query.filter_by(premium_number_id=number_id).all()
                    for contract in contracts:
                        db.session.delete(contract)

                    # Delete the number itself
                    db.session.delete(number)
                    deleted_count += 1
            except Exception as e:
                print(f"Error deleting number {number_id}: {str(e)}")
                continue

        db.session.commit()

        return jsonify({
            'success': True,
            'count': deleted_count,
            'message': f'تم حذف {deleted_count} رقم مميز بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/premium_numbers/view/<int:number_id>')
def view_premium_number(number_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    number = PremiumNumber.query.get_or_404(number_id)

    # Get auction information if exists
    auction = Auction.query.filter_by(premium_number_id=number_id).order_by(Auction.created_at.desc()).first()

    # Get contracts for this number
    contracts = Contract.query.filter_by(premium_number_id=number_id).all()

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تفاصيل الرقم المميز {{ number.number }} - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .number-display {
                font-size: 3em;
                font-weight: bold;
                color: #007bff;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            }
            .info-card {
                border-radius: 15px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                margin-bottom: 20px;
            }
            .auction-active {
                background: linear-gradient(135deg, #ffc107, #ff8c00);
                color: white;
                animation: pulse 2s infinite;
            }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('premium_numbers') }}">الأرقام المميزة</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Number Header -->
            <div class="info-card {% if number.status == 'auction' %}auction-active{% else %}bg-primary text-white{% endif %} text-center p-4">
                <div class="number-display">{{ number.number }}</div>
                <h3>رقم مميز - فئة {{ number.category }}</h3>
                <p class="lead">السعر الأساسي: {{ "{:,.0f}".format(number.price) }} ر.ق</p>
            </div>

            <div class="row">
                <!-- Number Information -->
                <div class="col-md-6">
                    <div class="info-card card">
                        <div class="card-header bg-info text-white">
                            <h5><i class="fas fa-info-circle"></i> معلومات الرقم</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <strong>الرقم:</strong>
                                </div>
                                <div class="col-6">
                                    {{ number.number }}
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-6">
                                    <strong>الفئة:</strong>
                                </div>
                                <div class="col-6">
                                    <span class="badge bg-primary">{{ number.category }}</span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-6">
                                    <strong>السعر الأساسي:</strong>
                                </div>
                                <div class="col-6">
                                    {{ "{:,.0f}".format(number.price) }} ر.ق
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-6">
                                    <strong>الحالة:</strong>
                                </div>
                                <div class="col-6">
                                    {% if number.status == 'available' %}
                                        <span class="badge bg-success">متاح</span>
                                    {% elif number.status == 'sold' %}
                                        <span class="badge bg-danger">مباع</span>
                                    {% elif number.status == 'auction' %}
                                        <span class="badge bg-warning">في المزاد</span>
                                    {% elif number.status == 'reserved' %}
                                        <span class="badge bg-secondary">محجوز</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Auction Information -->
                <div class="col-md-6">
                    {% if auction %}
                    <div class="info-card card">
                        <div class="card-header bg-warning text-dark">
                            <h5><i class="fas fa-gavel"></i> معلومات المزاد</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-6">
                                    <strong>حالة المزاد:</strong>
                                </div>
                                <div class="col-6">
                                    {% if auction.status == 'active' %}
                                        <span class="badge bg-success">نشط</span>
                                    {% elif auction.status == 'scheduled' %}
                                        <span class="badge bg-warning">مجدول</span>
                                    {% elif auction.status == 'ended' %}
                                        <span class="badge bg-secondary">منتهي</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-6">
                                    <strong>السعر الحالي:</strong>
                                </div>
                                <div class="col-6">
                                    <strong class="text-success">{{ "{:,.0f}".format(auction.current_price if auction.current_price > 0 else auction.starting_price) }} ر.ق</strong>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-6">
                                    <strong>عدد المزايدات:</strong>
                                </div>
                                <div class="col-6">
                                    {{ auction.total_bids }}
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-6">
                                    <strong>ينتهي في:</strong>
                                </div>
                                <div class="col-6">
                                    {{ auction.end_time.strftime('%Y-%m-%d %H:%M') }}
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <a href="{{ url_for('auction_details', auction_id=auction.id) }}" class="btn btn-warning">
                                    <i class="fas fa-eye"></i> عرض المزاد
                                </a>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="info-card card">
                        <div class="card-header bg-secondary text-white">
                            <h5><i class="fas fa-gavel"></i> المزاد</h5>
                        </div>
                        <div class="card-body text-center">
                            {% if number.status == 'available' %}
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i> هذا الرقم متاح لإنشاء مزاد
                                </div>
                                <a href="{{ url_for('create_auction_for_number', number_id=number.id) }}" class="btn btn-success btn-lg">
                                    <i class="fas fa-gavel"></i> إنشاء مزاد للرقم {{ number.number }}
                                </a>
                                <p class="mt-2 text-muted">
                                    <small>سيتم تعبئة بيانات المزاد تلقائياً بناءً على معلومات الرقم</small>
                                </p>
                            {% elif number.status == 'sold' %}
                                <div class="alert alert-info">
                                    <i class="fas fa-handshake"></i> تم بيع هذا الرقم بنجاح
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> لا يمكن إنشاء مزاد لهذا الرقم حالياً
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Contracts History -->
            {% if contracts %}
            <div class="info-card card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-file-contract"></i> تاريخ العقود</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>رقم العقد</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for contract in contracts %}
                                <tr>
                                    <td>{{ contract.contract_number }}</td>
                                    <td>{{ contract.customer.name_ar }}</td>
                                    <td>{{ "{:,.0f}".format(contract.total_amount) }} ر.ق</td>
                                    <td>{{ contract.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if contract.status == 'active' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% elif contract.status == 'completed' %}
                                            <span class="badge bg-primary">مكتمل</span>
                                        {% elif contract.status == 'cancelled' %}
                                            <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="text-center mb-4">
                <a href="{{ url_for('premium_numbers') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
                <a href="{{ url_for('edit_premium_number', number_id=number.id) }}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل الرقم
                </a>
                {% if number.status == 'available' %}
                    <a href="{{ url_for('create_auction_for_number', number_id=number.id) }}" class="btn btn-success">
                        <i class="fas fa-gavel"></i> إنشاء مزاد
                    </a>
                {% endif %}
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''', number=number, auction=auction, contracts=contracts)

@app.route('/premium_numbers/create_auction/<int:number_id>')
def create_auction_for_number(number_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    number = PremiumNumber.query.get_or_404(number_id)

    if number.status != 'available':
        flash('لا يمكن إنشاء مزاد لهذا الرقم لأنه غير متاح', 'error')
        return redirect(url_for('premium_numbers'))

    # Redirect to create auction page with pre-filled number
    return redirect(url_for('create_auction', number_id=number_id))

@app.route('/premium_numbers/start_auction/<int:number_id>')
def start_auction(number_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        number = PremiumNumber.query.get_or_404(number_id)
        number.status = 'auction'
        db.session.commit()
        flash(f'تم بدء المزاد للرقم {number.number}', 'success')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')

    return redirect(url_for('auction'))

# ==================== إدارة العملاء ====================
@app.route('/customers')
def customers():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    customers_list = Customer.query.all()
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إدارة العملاء - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    <a class="nav-link" href="{{ url_for('cars') }}">السيارات</a>
                    <a class="nav-link" href="{{ url_for('premium_numbers') }}">الأرقام المميزة</a>
                    <a class="nav-link" href="{{ url_for('customers') }}">العملاء</a>
                    <a class="nav-link" href="{{ url_for('contracts') }}">العقود</a>
                    <a class="nav-link" href="{{ url_for('auction') }}">المزاد</a>
                    <a class="nav-link" href="{{ url_for('logout') }}">خروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users"></i> إدارة العملاء</h2>
                <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة عميل جديد
                </a>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>عدد العقود</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in customers %}
                                <tr>
                                    <td>{{ customer.name_ar }}</td>
                                    <td>{{ customer.phone }}</td>
                                    <td>{{ customer.email or 'غير محدد' }}</td>
                                    <td>{{ customer.contracts|length }}</td>
                                    <td>
                                        <a href="{{ url_for('edit_customer', customer_id=customer.id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <a href="{{ url_for('view_customer', customer_id=customer.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', customers=customers_list)

@app.route('/customers/add', methods=['GET', 'POST'])
def add_customer():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    if request.method == 'POST':
        try:
            customer = Customer(
                name_ar=request.form['name_ar'],
                phone=request.form['phone'],
                email=request.form['email'] if request.form['email'] else None
            )
            db.session.add(customer)
            db.session.commit()
            flash('تم إضافة العميل بنجاح', 'success')
            return redirect(url_for('customers'))
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إضافة عميل جديد - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('customers') }}">العملاء</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <h2><i class="fas fa-plus"></i> إضافة عميل جديد</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم بالعربية</label>
                                    <input type="text" class="form-control" name="name_ar" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" name="phone" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني (اختياري)</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ العميل
                            </button>
                            <a href="{{ url_for('customers') }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/customers/edit/<int:customer_id>', methods=['GET', 'POST'])
def edit_customer(customer_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    customer = Customer.query.get_or_404(customer_id)

    if request.method == 'POST':
        try:
            customer.name_ar = request.form['name_ar']
            customer.phone = request.form['phone']
            customer.email = request.form['email'] if request.form['email'] else None

            db.session.commit()
            flash('تم تحديث بيانات العميل بنجاح', 'success')
            return redirect(url_for('customers'))
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تعديل العميل - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('customers') }}">العملاء</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <h2><i class="fas fa-edit"></i> تعديل بيانات العميل</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم بالعربية</label>
                                    <input type="text" class="form-control" name="name_ar" value="{{ customer.name_ar }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" name="phone" value="{{ customer.phone }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني (اختياري)</label>
                                    <input type="email" class="form-control" name="email" value="{{ customer.email or '' }}">
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{{ url_for('customers') }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', customer=customer)

@app.route('/customers/view/<int:customer_id>')
def view_customer(customer_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    customer = Customer.query.get_or_404(customer_id)

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تفاصيل العميل - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .customer-detail-card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('customers') }}">العملاء</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user"></i> تفاصيل العميل</h2>
                <div>
                    <a href="{{ url_for('edit_customer', customer_id=customer.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                    <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card customer-detail-card">
                        <div class="card-header bg-primary text-white">
                            <h4><i class="fas fa-info-circle"></i> معلومات العميل</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5><i class="fas fa-user"></i> الاسم</h5>
                                    <p class="lead">{{ customer.name_ar }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h5><i class="fas fa-phone"></i> الهاتف</h5>
                                    <p class="lead">{{ customer.phone }}</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <h5><i class="fas fa-envelope"></i> البريد الإلكتروني</h5>
                                    <p class="lead">{{ customer.email or 'غير محدد' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card customer-detail-card">
                        <div class="card-header bg-info text-white">
                            <h5><i class="fas fa-file-contract"></i> عقود العميل</h5>
                        </div>
                        <div class="card-body">
                            {% if customer.contracts %}
                                {% for contract in customer.contracts %}
                                <div class="mb-2">
                                    <strong>{{ contract.contract_number }}</strong><br>
                                    <small class="text-muted">
                                        {% if contract.contract_type == 'car_sale' %}
                                            بيع سيارة
                                        {% elif contract.contract_type == 'premium_number_sale' %}
                                            بيع رقم مميز
                                        {% else %}
                                            عقد مدمج
                                        {% endif %}
                                    </small><br>
                                    <small class="text-success">{{ "{:,.0f}".format(contract.total_amount) }} ر.ق</small>
                                </div>
                                <hr>
                                {% endfor %}

                                <div class="mt-3">
                                    <strong>إجمالي المشتريات:</strong><br>
                                    <h4 class="text-success">
                                        {{ "{:,.0f}".format(customer.contracts|sum(attribute='total_amount')) }} ر.ق
                                    </h4>
                                </div>
                            {% else %}
                                <p class="text-muted">لا توجد عقود لهذا العميل</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="card customer-detail-card mt-3">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <a href="{{ url_for('add_contract') }}?customer_id={{ customer.id }}" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-file-contract"></i> إنشاء عقد جديد
                            </a>
                            <a href="{{ url_for('edit_customer', customer_id=customer.id) }}" class="btn btn-warning w-100">
                                <i class="fas fa-edit"></i> تعديل البيانات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', customer=customer)

# ==================== نظام المزاد المتطور ====================
class Auction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    premium_number_id = db.Column(db.Integer, db.ForeignKey('premium_number.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    starting_price = db.Column(db.Float, nullable=False)
    reserve_price = db.Column(db.Float)  # السعر الاحتياطي
    current_price = db.Column(db.Float, default=0)
    bid_increment = db.Column(db.Float, default=1000)  # الحد الأدنى للزيادة
    start_time = db.Column(db.DateTime, nullable=False)
    end_time = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(20), default='scheduled')  # scheduled, active, ended, cancelled
    winner_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    winning_bid_id = db.Column(db.Integer, db.ForeignKey('bid.id'))
    total_bids = db.Column(db.Integer, default=0)
    views = db.Column(db.Integer, default=0)
    auto_extend = db.Column(db.Boolean, default=True)  # تمديد تلقائي
    extend_time = db.Column(db.Integer, default=300)  # 5 دقائق بالثواني
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Relationships
    premium_number = db.relationship('PremiumNumber', backref='auctions')
    winner = db.relationship('Customer', foreign_keys=[winner_id], backref='won_auctions')
    winning_bid = db.relationship('Bid', foreign_keys=[winning_bid_id], post_update=True)
    creator = db.relationship('User', backref='created_auctions')

class Bid(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    auction_id = db.Column(db.Integer, db.ForeignKey('auction.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    bid_amount = db.Column(db.Float, nullable=False)
    bid_time = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='active')  # active, outbid, winning, withdrawn
    ip_address = db.Column(db.String(45))  # لتتبع IP
    user_agent = db.Column(db.String(255))  # لتتبع المتصفح
    is_auto_bid = db.Column(db.Boolean, default=False)  # مزايدة تلقائية
    max_auto_bid = db.Column(db.Float)  # الحد الأقصى للمزايدة التلقائية

    # Relationships
    auction = db.relationship('Auction', foreign_keys=[auction_id], backref='bids')
    customer = db.relationship('Customer', backref='bids')

class AuctionWatch(db.Model):
    """متابعة المزادات"""
    id = db.Column(db.Integer, primary_key=True)
    auction_id = db.Column(db.Integer, db.ForeignKey('auction.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    notifications_enabled = db.Column(db.Boolean, default=True)

    # Relationships
    auction = db.relationship('Auction', backref='watchers')
    customer = db.relationship('Customer', backref='watched_auctions')

class Notification(db.Model):
    """إشعارات النظام"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    auction_id = db.Column(db.Integer, db.ForeignKey('auction.id'), nullable=True)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)  # auction_start, new_bid, auction_end
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='notifications')
    auction = db.relationship('Auction', backref='notifications')

def create_notification(user_id, auction_id, title, message, notification_type):
    """إنشاء إشعار جديد"""
    try:
        notification = Notification(
            user_id=user_id,
            auction_id=auction_id,
            title=title,
            message=message,
            notification_type=notification_type
        )
        db.session.add(notification)
        db.session.commit()
        return True
    except Exception as e:
        db.session.rollback()
        print(f"خطأ في إنشاء الإشعار: {e}")
        return False

def notify_auction_watchers(auction_id, title, message, notification_type):
    """إرسال إشعارات لجميع متابعي المزاد"""
    try:
        watchers = AuctionWatch.query.filter_by(auction_id=auction_id).all()
        for watcher in watchers:
            create_notification(
                user_id=watcher.customer_id,
                auction_id=auction_id,
                title=title,
                message=message,
                notification_type=notification_type
            )
        return len(watchers)
    except Exception as e:
        print(f"خطأ في إرسال الإشعارات: {e}")
        return 0

class AuctionActivity(db.Model):
    """سجل أنشطة المزاد"""
    id = db.Column(db.Integer, primary_key=True)
    auction_id = db.Column(db.Integer, db.ForeignKey('auction.id'), nullable=False)
    activity_type = db.Column(db.String(30), nullable=False)  # bid, view, watch, unwatch, extend
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    details = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    auction = db.relationship('Auction', backref='activities')
    customer = db.relationship('Customer', backref='auction_activities')

@app.route('/auction')
def auction():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # Get active auctions
    from datetime import datetime
    now = datetime.utcnow()

    # Update auction statuses based on time
    # Move scheduled auctions to active if start time has passed
    scheduled_to_active = Auction.query.filter(
        Auction.status == 'scheduled',
        Auction.start_time <= now,
        Auction.end_time > now
    ).all()

    for auction in scheduled_to_active:
        auction.status = 'active'
        # إرسال إشعارات لمتابعي المزاد عند بدء المزاد
        notify_auction_watchers(
            auction.id,
            f"بدأ المزاد: {auction.title}",
            f"بدأ المزاد للرقم المميز {auction.premium_number.number} بسعر ابتدائي {auction.starting_price:,.0f} ر.ق",
            "auction_start"
        )

    # Move active auctions to ended if end time has passed
    active_to_ended = Auction.query.filter(
        Auction.status == 'active',
        Auction.end_time <= now
    ).all()

    for auction in active_to_ended:
        auction.status = 'ended'
        # إرسال إشعارات لمتابعي المزاد عند انتهاء المزاد
        notify_auction_watchers(
            auction.id,
            f"انتهى المزاد: {auction.title}",
            f"انتهى المزاد للرقم المميز {auction.premium_number.number}",
            "auction_end"
        )
        # Update premium number status back to available if no winner
        if not auction.winner:
            auction.premium_number.status = 'available'

    db.session.commit()

    active_auctions = Auction.query.filter(
        Auction.status == 'active',
        Auction.end_time > now
    ).order_by(Auction.end_time.asc()).all()

    upcoming_auctions = Auction.query.filter(
        Auction.status == 'scheduled',
        Auction.start_time > now
    ).order_by(Auction.start_time.asc()).all()

    ended_auctions = Auction.query.filter(
        Auction.status == 'ended'
    ).order_by(Auction.end_time.desc()).limit(5).all()

    # Get user's watched auctions
    user_id = session['user_id']
    user_watched_auctions = [watch.auction_id for watch in AuctionWatch.query.filter_by(customer_id=user_id).all()]

    # Get all customers for bidding forms
    customers = Customer.query.all()



    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام المزاد المتطور - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .auction-card {
                border: 2px solid #007bff;
                border-radius: 15px;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }
            .auction-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,123,255,0.3);
            }
            .auction-card.ending-soon {
                border-color: #dc3545;
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
                100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
            }
            .bid-section {
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                border-radius: 10px;
                padding: 20px;
                border: 1px solid #dee2e6;
            }
            .countdown-timer {
                font-family: 'Courier New', monospace;
                font-size: 1.2em;
                font-weight: bold;
            }
            .live-indicator {
                position: absolute;
                top: 10px;
                left: 10px;
                background: #dc3545;
                color: white;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 0.8em;
                animation: blink 1s infinite;
            }
            @keyframes blink {
                0%, 50% { opacity: 1; }
                51%, 100% { opacity: 0.5; }
            }
            .bid-history {
                max-height: 200px;
                overflow-y: auto;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                background: white;
            }
            .winning-bid {
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 15px;
            }
            .auction-stats {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 15px;
            }
            .tab-content {
                border: 1px solid #dee2e6;
                border-top: none;
                border-radius: 0 0 10px 10px;
                padding: 20px;
            }
            .nav-tabs .nav-link.active {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border-color: #007bff;
            }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    <a class="nav-link" href="{{ url_for('cars') }}">السيارات</a>
                    <a class="nav-link" href="{{ url_for('premium_numbers') }}">الأرقام المميزة</a>
                    <a class="nav-link" href="{{ url_for('customers') }}">العملاء</a>
                    <a class="nav-link" href="{{ url_for('contracts') }}">العقود</a>
                    <a class="nav-link" href="{{ url_for('auction') }}">المزاد</a>
                    <a class="nav-link" href="{{ url_for('notifications') }}">
                        <i class="fas fa-bell"></i> الإشعارات
                    </a>
                    <a class="nav-link" href="{{ url_for('admin_panel') }}">لوحة التحكم</a>
                    <a class="nav-link" href="{{ url_for('logout') }}">خروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-gavel"></i> نظام المزاد المتطور</h2>
                <div>
                    <a href="{{ url_for('create_auction') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إنشاء مزاد جديد
                    </a>
                    <a href="{{ url_for('auction_management') }}" class="btn btn-secondary">
                        <i class="fas fa-cogs"></i> إدارة المزادات
                    </a>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs" id="auctionTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="active-tab" data-bs-toggle="tab" data-bs-target="#active" type="button" role="tab">
                        <i class="fas fa-fire"></i> المزادات النشطة ({{ active_auctions|length }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="upcoming-tab" data-bs-toggle="tab" data-bs-target="#upcoming" type="button" role="tab">
                        <i class="fas fa-clock"></i> المزادات القادمة ({{ upcoming_auctions|length }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="ended-tab" data-bs-toggle="tab" data-bs-target="#ended" type="button" role="tab">
                        <i class="fas fa-flag-checkered"></i> المزادات المنتهية
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="auctionTabContent">
                <!-- Active Auctions -->
                <div class="tab-pane fade show active" id="active" role="tabpanel">
                    {% if active_auctions %}
                        <div class="row">
                            {% for auction in active_auctions %}
                            <div class="col-md-6 mb-4">
                                <div class="card auction-card {% if (auction.end_time - now).total_seconds() < 3600 %}ending-soon{% endif %}">
                                    {% if (auction.end_time - now).total_seconds() < 3600 %}
                                        <div class="live-indicator">
                                            <i class="fas fa-circle"></i> ينتهي قريباً
                                        </div>
                                    {% endif %}

                                    <div class="card-header bg-primary text-white">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <!-- Mini Qatar License Plate -->
                                                <div class="mini-qatar-plate">
                                                    <div class="mini-plate-content">
                                                        <div class="mini-plate-number">{{ auction.premium_number.number }}</div>
                                                    </div>
                                                </div>
                                                <span class="badge bg-warning mt-2">{{ auction.premium_number.category }}</span>
                                            </div>
                                            <div class="text-end">
                                                <small>المزاد #{{ auction.id }}</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card-body">
                                        <!-- Countdown Timer -->
                                        <div class="countdown-timer text-center mb-3 p-2 bg-light rounded">
                                            <div id="countdown-{{ auction.id }}" data-end-time="{{ auction.end_time.isoformat() }}">
                                                <i class="fas fa-hourglass-half"></i> جاري التحميل...
                                            </div>
                                        </div>

                                        <!-- Current Winning Bid -->
                                        {% set winning_bid = auction.bids|selectattr('status', 'equalto', 'active')|list|sort(attribute='bid_amount', reverse=true)|first %}
                                        {% if winning_bid %}
                                            <div class="winning-bid text-center">
                                                <h5><i class="fas fa-trophy"></i> المزايدة الرابحة</h5>
                                                <h3>{{ "{:,.0f}".format(winning_bid.bid_amount) }} ر.ق</h3>
                                                <p class="mb-0">بواسطة: {{ winning_bid.customer.name_ar }}</p>
                                                <small>{{ winning_bid.bid_time.strftime('%H:%M:%S') }}</small>
                                            </div>
                                        {% else %}
                                            <div class="bid-section text-center">
                                                <h5>السعر الابتدائي</h5>
                                                <h3 class="text-primary">{{ "{:,.0f}".format(auction.starting_price) }} ر.ق</h3>
                                                <p class="text-muted">لا توجد مزايدات حتى الآن</p>
                                            </div>
                                        {% endif %}

                                        <!-- Auction Stats -->
                                        <div class="auction-stats">
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <strong>{{ auction.total_bids }}</strong><br>
                                                    <small>مزايدة</small>
                                                </div>
                                                <div class="col-4">
                                                    <strong>{{ auction.views }}</strong><br>
                                                    <small>مشاهدة</small>
                                                </div>
                                                <div class="col-4">
                                                    <strong>{{ auction.watchers|length }}</strong><br>
                                                    <small>متابع</small>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Bidding Form -->
                                        <form method="POST" action="{{ url_for('place_bid_new', auction_id=auction.id) }}" class="mt-3">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <select class="form-select mb-2" name="customer_id" required>
                                                        <option value="">اختر العميل</option>
                                                        {% for customer in customers %}
                                                        <option value="{{ customer.id }}">{{ customer.name_ar }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    {% set min_bid = (winning_bid.bid_amount + auction.bid_increment) if winning_bid else auction.starting_price %}
                                                    <input type="number" class="form-control mb-2" name="bid_amount"
                                                           placeholder="مبلغ المزايدة" step="{{ auction.bid_increment }}"
                                                           min="{{ min_bid }}" value="{{ min_bid }}" required>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <button type="submit" class="btn btn-success w-100">
                                                        <i class="fas fa-hand-paper"></i> مزايدة {{ "{:,.0f}".format(min_bid) }} ر.ق
                                                    </button>
                                                </div>
                                                <div class="col-md-4">
                                                    <a href="{{ url_for('auction_details', auction_id=auction.id) }}" class="btn btn-outline-primary w-100">
                                                        <i class="fas fa-eye"></i> التفاصيل
                                                    </a>
                                                </div>
                                            </div>
                                        </form>

                                        <!-- Quick Actions -->
                                        <div class="mt-2">
                                            <button class="btn btn-sm btn-outline-info" onclick="watchAuction({{ auction.id }})">
                                                <i class="fas fa-eye"></i> متابعة
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning" onclick="shareAuction({{ auction.id }})">
                                                <i class="fas fa-share"></i> مشاركة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-gavel fa-3x text-muted mb-3"></i>
                            <h4>لا توجد مزادات نشطة حالياً</h4>
                            <p class="text-muted">تابع المزادات القادمة أو أنشئ مزاد جديد</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Upcoming Auctions -->
                <div class="tab-pane fade" id="upcoming" role="tabpanel">
                    {% if upcoming_auctions %}
                        <div class="row">
                            {% for auction in upcoming_auctions %}
                            <div class="col-md-6 mb-4">
                                <div class="card auction-card">
                                    <div class="card-header bg-info text-white">
                                        <div class="text-center">
                                            <!-- Mini Qatar License Plate -->
                                            <div class="mini-qatar-plate">
                                                <div class="mini-plate-content">
                                                    <div class="mini-plate-number">{{ auction.premium_number.number }}</div>
                                                </div>
                                            </div>
                                            <span class="badge bg-warning mt-2">{{ auction.premium_number.category }}</span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="text-center mb-3">
                                            <h5><i class="fas fa-clock"></i> يبدأ في</h5>
                                            <h4 class="text-primary">{{ auction.start_time.strftime('%Y-%m-%d %H:%M') }}</h4>
                                        </div>

                                        <div class="auction-stats">
                                            <div class="row text-center">
                                                <div class="col-6">
                                                    <strong>{{ "{:,.0f}".format(auction.starting_price) }} ر.ق</strong><br>
                                                    <small>السعر الابتدائي</small>
                                                </div>
                                                <div class="col-6">
                                                    <strong>{{ auction.watchers|length }}</strong><br>
                                                    <small>متابع</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-3">
                                            {% set is_watching_auction = auction.id in user_watched_auctions %}
                                            <button id="watchBtn{{ auction.id }}"
                                                    class="btn btn-{{ 'success' if is_watching_auction else 'outline-info' }} w-100"
                                                    onclick="toggleAuctionWatch({{ auction.id }})">
                                                <i class="fas fa-{{ 'check' if is_watching_auction else 'bell' }}"></i>
                                                {{ 'مُفعل التذكير' if is_watching_auction else 'تذكيرني عند البدء' }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-calendar fa-3x text-muted mb-3"></i>
                            <h4>لا توجد مزادات مجدولة</h4>
                        </div>
                    {% endif %}
                </div>

                <!-- Ended Auctions -->
                <div class="tab-pane fade" id="ended" role="tabpanel">
                    {% if ended_auctions %}
                        <div class="row">
                            {% for auction in ended_auctions %}
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <div class="text-center">
                                            <!-- Mini Qatar License Plate -->
                                            <div class="mini-qatar-plate">
                                                <div class="mini-plate-content">
                                                    <div class="mini-plate-number">{{ auction.premium_number.number }}</div>
                                                </div>
                                            </div>
                                            <span class="badge bg-light text-dark mt-2">منتهي</span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        {% if auction.winner %}
                                            <div class="winning-bid text-center">
                                                <h6><i class="fas fa-trophy"></i> الفائز</h6>
                                                <h4>{{ auction.winner.name_ar }}</h4>
                                                <h5>{{ "{:,.0f}".format(auction.winning_bid.bid_amount) }} ر.ق</h5>
                                            </div>
                                        {% else %}
                                            <div class="text-center text-muted">
                                                <p>لم يحقق السعر الاحتياطي</p>
                                            </div>
                                        {% endif %}

                                        <small class="text-muted">
                                            انتهى في: {{ auction.end_time.strftime('%Y-%m-%d %H:%M') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h4>لا توجد مزادات منتهية</h4>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            // Countdown Timer Function
            function updateCountdowns() {
                document.querySelectorAll('[id^="countdown-"]').forEach(function(element) {
                    const endTime = new Date(element.dataset.endTime + 'Z').getTime();
                    const now = new Date().getTime();
                    const distance = endTime - now;

                    if (distance > 0) {
                        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                        let timeString = '';
                        if (days > 0) timeString += days + 'ي ';
                        timeString += String(hours).padStart(2, '0') + ':' +
                                     String(minutes).padStart(2, '0') + ':' +
                                     String(seconds).padStart(2, '0');

                        element.innerHTML = '<i class="fas fa-hourglass-half"></i> ' + timeString;

                        // Change color when less than 1 hour
                        if (distance < 3600000) {
                            element.style.color = '#dc3545';
                            element.style.fontWeight = 'bold';
                        }
                    } else {
                        element.innerHTML = '<i class="fas fa-flag-checkered"></i> انتهى المزاد';
                        element.style.color = '#6c757d';
                        // Refresh page when auction ends
                        setTimeout(() => location.reload(), 2000);
                    }
                });
            }

            // Update countdowns every second
            setInterval(updateCountdowns, 1000);
            updateCountdowns(); // Initial call

            // Toggle Auction Watch Function
            function toggleAuctionWatch(auctionId) {
                const button = document.getElementById('watchBtn' + auctionId);
                const originalText = button.innerHTML;

                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';

                fetch('/auction/watch/' + auctionId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.action === 'added') {
                            button.className = 'btn btn-success w-100';
                            button.innerHTML = '<i class="fas fa-check"></i> مُفعل التذكير';
                            showNotification('تم تفعيل التذكير! ستتلقى تنبيهات في النظام عند بدء المزاد والمزايدات الجديدة', 'success');
                        } else {
                            button.className = 'btn btn-outline-info w-100';
                            button.innerHTML = '<i class="fas fa-bell"></i> تذكيرني عند البدء';
                            showNotification('تم إلغاء التذكير', 'info');
                        }
                    } else {
                        showNotification('حدث خطأ: ' + data.message, 'error');
                        button.innerHTML = originalText;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في الاتصال', 'error');
                    button.innerHTML = originalText;
                })
                .finally(() => {
                    button.disabled = false;
                });
            }

            // Legacy function for compatibility
            function watchAuction(auctionId) {
                toggleAuctionWatch(auctionId);
            }

            // Show notification function
            function showNotification(message, type) {
                // Remove existing notifications
                const existingNotif = document.querySelector('.notification-alert');
                if (existingNotif) existingNotif.remove();

                // Create notification
                const notification = document.createElement('div');
                notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show notification-alert`;
                notification.style.position = 'fixed';
                notification.style.top = '20px';
                notification.style.right = '20px';
                notification.style.zIndex = '9999';
                notification.style.minWidth = '300px';
                notification.innerHTML = `
                    <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.body.appendChild(notification);

                // Auto remove after 4 seconds
                setTimeout(() => {
                    if (notification) notification.remove();
                }, 4000);
            }

            // Share Auction Function
            function shareAuction(auctionId) {
                const url = window.location.origin + '/auction/details/' + auctionId;
                if (navigator.share) {
                    navigator.share({
                        title: 'مزاد رقم مميز',
                        text: 'شاهد هذا المزاد المثير!',
                        url: url
                    });
                } else {
                    // Fallback for browsers that don't support Web Share API
                    navigator.clipboard.writeText(url).then(() => {
                        alert('تم نسخ رابط المزاد');
                    });
                }
            }

            // Auto-refresh active auctions every 30 seconds
            if (document.querySelector('#active.show')) {
                setInterval(() => {
                    // Only refresh if user is still on active tab
                    if (document.querySelector('#active.show')) {
                        fetch('/auction/refresh')
                        .then(response => response.json())
                        .then(data => {
                            // Update bid counts and amounts without full page reload
                            data.auctions.forEach(auction => {
                                const element = document.querySelector(`#auction-${auction.id}`);
                                if (element) {
                                    // Update winning bid if changed
                                    // This would require more complex DOM manipulation
                                }
                            });
                        })
                        .catch(error => console.error('Refresh error:', error));
                    }
                }, 30000);
            }

            // Form validation for bidding
            document.querySelectorAll('form[action*="place_bid"]').forEach(form => {
                form.addEventListener('submit', function(e) {
                    const bidAmount = this.querySelector('input[name="bid_amount"]').value;
                    const minBid = this.querySelector('input[name="bid_amount"]').min;

                    if (parseFloat(bidAmount) < parseFloat(minBid)) {
                        e.preventDefault();
                        alert('مبلغ المزايدة يجب أن يكون أعلى من ' + minBid + ' ر.ق');
                        return false;
                    }

                    // Confirm bid
                    if (!confirm('هل أنت متأكد من تقديم مزايدة بمبلغ ' + bidAmount + ' ر.ق؟')) {
                        e.preventDefault();
                        return false;
                    }
                });
            });

            // Sound notification for new bids (optional)
            function playBidSound() {
                // Create audio context for bid notification
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = 800;
                oscillator.type = 'sine';
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);
            }
        </script>
    </body>
    </html>
    ''',
    active_auctions=active_auctions,
    upcoming_auctions=upcoming_auctions,
    ended_auctions=ended_auctions,
    customers=customers,
    user_watched_auctions=user_watched_auctions,
    now=now)

@app.route('/place_bid_new/<int:auction_id>', methods=['POST'])
def place_bid_new(auction_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        from datetime import datetime

        auction = Auction.query.get_or_404(auction_id)
        customer_id = request.form['customer_id']
        bid_amount = float(request.form['bid_amount'])

        # Check if auction is still active
        now = datetime.utcnow()
        if auction.status != 'active' or auction.end_time <= now:
            flash('هذا المزاد غير نشط أو انتهى', 'error')
            return redirect(url_for('auction'))

        # Check minimum bid
        current_highest = Bid.query.filter_by(auction_id=auction_id, status='active').order_by(Bid.bid_amount.desc()).first()
        minimum_bid = (current_highest.bid_amount + auction.bid_increment) if current_highest else auction.starting_price

        if bid_amount < minimum_bid:
            flash(f'يجب أن تكون المزايدة أعلى من {minimum_bid:,.0f} ر.ق', 'error')
            return redirect(url_for('auction'))

        # Mark previous bids as outbid
        if current_highest:
            current_highest.status = 'outbid'

        # Create new bid
        bid = Bid(
            auction_id=auction_id,
            customer_id=customer_id,
            bid_amount=bid_amount,
            status='active',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')[:255]
        )
        db.session.add(bid)

        # Update auction stats
        auction.current_price = bid_amount
        auction.total_bids += 1

        # إرسال إشعارات لمتابعي المزاد عند مزايدة جديدة
        customer = Customer.query.get(customer_id)
        notify_auction_watchers(
            auction_id,
            f"مزايدة جديدة: {auction.title}",
            f"مزايدة جديدة من {customer.name_ar} بمبلغ {bid_amount:,.0f} ر.ق للرقم {auction.premium_number.number}",
            "new_bid"
        )

        # Auto-extend if bid is placed in last 5 minutes
        time_left = (auction.end_time - now).total_seconds()
        if time_left < auction.extend_time and auction.auto_extend:
            auction.end_time = datetime.utcnow() + timedelta(seconds=auction.extend_time)
            flash(f'تم تمديد المزاد {auction.extend_time//60} دقائق إضافية', 'info')

        # Log activity
        activity = AuctionActivity(
            auction_id=auction_id,
            activity_type='bid',
            customer_id=customer_id,
            details=f'مزايدة بمبلغ {bid_amount:,.0f} ر.ق'
        )
        db.session.add(activity)

        db.session.commit()
        flash('تم تقديم المزايدة بنجاح', 'success')

    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')

    return redirect(url_for('auction'))

@app.route('/auction/watch/<int:auction_id>', methods=['POST'])
def watch_auction(auction_id):
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مسجل دخول'})

    try:
        # Use user_id as customer_id for admin users
        # In a real system, you'd have separate customer and admin sessions
        user_id = session['user_id']

        # Get auction details for better messages
        auction = Auction.query.get_or_404(auction_id)
        auction_title = auction.title or f"المزاد #{auction_id}"

        # Check if already watching (using user_id as customer_id)
        existing_watch = AuctionWatch.query.filter_by(
            auction_id=auction_id,
            customer_id=user_id
        ).first()

        if existing_watch:
            # Instead of error, toggle the watch status
            db.session.delete(existing_watch)

            # Log activity
            activity = AuctionActivity(
                auction_id=auction_id,
                activity_type='unwatch',
                customer_id=user_id,
                details='إلغاء متابعة المزاد'
            )
            db.session.add(activity)

            db.session.commit()
            return jsonify({
                'success': True,
                'message': f'تم إلغاء متابعة "{auction_title}" بنجاح',
                'action': 'removed'
            })

        # Add to watch list
        watch = AuctionWatch(
            auction_id=auction_id,
            customer_id=user_id
        )
        db.session.add(watch)

        # Log activity
        activity = AuctionActivity(
            auction_id=auction_id,
            activity_type='watch',
            customer_id=user_id,
            details='بدء متابعة المزاد'
        )
        db.session.add(activity)

        db.session.commit()
        return jsonify({
            'success': True,
            'message': f'تم إضافة "{auction_title}" لقائمة المتابعة بنجاح',
            'action': 'added'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/create_auction', methods=['GET', 'POST'])
def create_auction():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # Get pre-selected number if provided
    selected_number_id = request.args.get('number_id', type=int)
    selected_number = None
    if selected_number_id:
        selected_number = PremiumNumber.query.get(selected_number_id)
        if selected_number and selected_number.status != 'available':
            flash('الرقم المحدد غير متاح للمزاد', 'error')
            selected_number = None

    if request.method == 'POST':
        try:
            from datetime import datetime, timedelta

            # Get form data
            premium_number_id = request.form['premium_number_id']
            title = request.form['title']
            description = request.form.get('description', '')
            starting_price = float(request.form['starting_price'])
            reserve_price = float(request.form['reserve_price']) if request.form.get('reserve_price') else None
            bid_increment = float(request.form.get('bid_increment', 1000))
            duration_minutes = int(request.form.get('duration_minutes', 1440))  # Default 24 hours
            auto_extend = 'auto_extend' in request.form

            # Calculate start and end times
            start_time = datetime.utcnow() + timedelta(minutes=5)  # Start in 5 minutes
            end_time = start_time + timedelta(minutes=duration_minutes)

            # Create auction
            auction = Auction(
                premium_number_id=premium_number_id,
                title=title,
                description=description,
                starting_price=starting_price,
                reserve_price=reserve_price,
                bid_increment=bid_increment,
                start_time=start_time,
                end_time=end_time,
                auto_extend=auto_extend,
                created_by=session['user_id']
            )

            db.session.add(auction)

            # Update premium number status
            premium_number = PremiumNumber.query.get(premium_number_id)
            premium_number.status = 'auction'

            db.session.commit()
            flash(f'تم إنشاء المزاد للرقم المميز {premium_number.number} بنجاح! سيبدأ المزاد خلال 5 دقائق.', 'success')
            return redirect(url_for('auction_details', auction_id=auction.id))

        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')

    # Get available premium numbers
    available_numbers = PremiumNumber.query.filter_by(status='available').all()

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إنشاء مزاد جديد - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('auction') }}">المزاد</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-plus"></i> إنشاء مزاد جديد</h2>
                <a href="{{ url_for('premium_numbers') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للأرقام المميزة
                </a>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Selected Number Preview -->
            {% if selected_number %}
            <div class="card mb-4 border-success">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-hashtag"></i> الرقم المميز المحدد</h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h2 class="text-success mb-0">{{ selected_number.number }}</h2>
                                <small class="text-muted">الرقم المميز</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <strong>الفئة:</strong><br>
                            <span class="badge bg-primary">{{ selected_number.category }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>السعر الأساسي:</strong><br>
                            <span class="text-success h5">{{ "{:,.0f}".format(selected_number.price) }} ر.ق</span>
                        </div>
                        <div class="col-md-3">
                            <strong>الحالة:</strong><br>
                            <span class="badge bg-success">متاح للمزاد</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Available Numbers Info -->
            {% if available_numbers %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                يوجد <strong>{{ available_numbers|length }}</strong> رقم مميز متاح للمزاد
            </div>
            {% endif %}

            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-gavel"></i> تفاصيل المزاد الجديد</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرقم المميز *</label>
                                    {% if selected_number %}
                                        <div class="input-group">
                                            <input type="hidden" name="premium_number_id" value="{{ selected_number.id }}">
                                            <input type="text" class="form-control bg-light" value="{{ selected_number.number }} - {{ selected_number.category }}" readonly>
                                            <span class="input-group-text bg-success text-white">
                                                <i class="fas fa-check"></i>
                                            </span>
                                        </div>
                                        <small class="text-success">تم اختيار الرقم مسبقاً</small>
                                    {% else %}
                                        <select class="form-select" name="premium_number_id" required onchange="updatePriceFromNumber()">
                                            <option value="">اختر الرقم المميز</option>
                                            {% if available_numbers %}
                                                {% for number in available_numbers %}
                                                <option value="{{ number.id }}" data-price="{{ number.price }}" data-category="{{ number.category }}">
                                                    {{ number.number }} - {{ number.category }} ({{ "{:,.0f}".format(number.price) }} ر.ق)
                                                </option>
                                                {% endfor %}
                                            {% else %}
                                                <option value="" disabled>لا توجد أرقام متاحة حالياً</option>
                                            {% endif %}
                                        </select>
                                        {% if not available_numbers %}
                                        <div class="alert alert-warning mt-2">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            لا توجد أرقام مميزة متاحة للمزاد حالياً.
                                            <a href="{{ url_for('premium_numbers') }}" class="alert-link">إدارة الأرقام المميزة</a>
                                        </div>
                                        {% endif %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عنوان المزاد *</label>
                                    <input type="text" class="form-control" name="title" id="auctionTitle"
                                           value="{% if selected_number %}مزاد الرقم المميز {{ selected_number.number }}{% endif %}" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف المزاد</label>
                            <textarea class="form-control" name="description" rows="3" id="auctionDescription">{% if selected_number %}مزاد علني للرقم المميز {{ selected_number.number }} من فئة {{ selected_number.category }}{% endif %}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">السعر الابتدائي (ر.ق) *</label>
                                    <input type="number" class="form-control" name="starting_price" id="startingPrice"
                                           step="100" min="0" value="{% if selected_number %}{{ selected_number.price }}{% endif %}" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">السعر الاحتياطي (ر.ق)</label>
                                    <input type="number" class="form-control" name="reserve_price" step="100" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الحد الأدنى للزيادة (ر.ق)</label>
                                    <input type="number" class="form-control" name="bid_increment" value="1000" step="100" min="100">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">مدة المزاد</label>
                                    <select class="form-select" name="duration_minutes" onchange="checkFastAuction(this.value)">
                                        <option value="2">دقيقتان (سريع جداً)</option>
                                        <option value="5">5 دقائق (سريع)</option>
                                        <option value="10">10 دقائق</option>
                                        <option value="15">15 دقيقة</option>
                                        <option value="30">30 دقيقة (نصف ساعة)</option>
                                        <option value="60">ساعة واحدة</option>
                                        <option value="120">ساعتان</option>
                                        <option value="180">3 ساعات</option>
                                        <option value="360">6 ساعات</option>
                                        <option value="720">12 ساعة</option>
                                        <option value="1440" selected>24 ساعة (يوم)</option>
                                        <option value="2880">48 ساعة (يومان)</option>
                                        <option value="4320">72 ساعة (3 أيام)</option>
                                        <option value="10080">أسبوع</option>
                                    </select>
                                    <div id="fastAuctionWarning" class="alert alert-warning mt-2" style="display: none;">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <strong>تحذير:</strong> المزادات السريعة (أقل من 15 دقيقة) مناسبة للاختبار أو العروض الخاصة فقط!
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" name="auto_extend" id="auto_extend" checked>
                                        <label class="form-check-label" for="auto_extend">
                                            تمديد تلقائي (5 دقائق عند المزايدة في النهاية)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-gavel"></i> إنشاء المزاد
                            </button>
                            <a href="{{ url_for('auction') }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function updatePriceFromNumber() {
                const select = document.querySelector('select[name="premium_number_id"]');
                const selectedOption = select.options[select.selectedIndex];

                if (selectedOption.value) {
                    const price = selectedOption.dataset.price;
                    const category = selectedOption.dataset.category;
                    const number = selectedOption.text.split(' - ')[0];

                    // Update starting price
                    document.getElementById('startingPrice').value = price;

                    // Update title
                    document.getElementById('auctionTitle').value = 'مزاد الرقم المميز ' + number;

                    // Update description
                    document.getElementById('auctionDescription').value =
                        'مزاد علني للرقم المميز ' + number + ' من فئة ' + category;

                    // Show success message
                    showNumberSelectedMessage(number, category, price);
                } else {
                    // Clear fields
                    document.getElementById('startingPrice').value = '';
                    document.getElementById('auctionTitle').value = '';
                    document.getElementById('auctionDescription').value = '';
                    hideNumberSelectedMessage();
                }
            }

            function showNumberSelectedMessage(number, category, price) {
                // Remove existing message
                const existingMsg = document.getElementById('numberSelectedMsg');
                if (existingMsg) existingMsg.remove();

                // Create new message
                const msg = document.createElement('div');
                msg.id = 'numberSelectedMsg';
                msg.className = 'alert alert-success mt-2';
                msg.innerHTML = `
                    <i class="fas fa-check-circle"></i>
                    تم اختيار الرقم <strong>${number}</strong> من فئة <strong>${category}</strong>
                    بسعر ابتدائي <strong>${parseInt(price).toLocaleString()}</strong> ر.ق
                `;

                // Insert after the select element
                const selectDiv = document.querySelector('select[name="premium_number_id"]').parentElement;
                selectDiv.appendChild(msg);
            }

            function hideNumberSelectedMessage() {
                const msg = document.getElementById('numberSelectedMsg');
                if (msg) msg.remove();
            }

            // Form validation
            function validateForm() {
                const numberSelect = document.querySelector('select[name="premium_number_id"]');
                const title = document.getElementById('auctionTitle');
                const startingPrice = document.getElementById('startingPrice');

                if (!numberSelect.value) {
                    alert('يرجى اختيار رقم مميز');
                    numberSelect.focus();
                    return false;
                }

                if (!title.value.trim()) {
                    alert('يرجى إدخال عنوان المزاد');
                    title.focus();
                    return false;
                }

                if (!startingPrice.value || parseFloat(startingPrice.value) <= 0) {
                    alert('يرجى إدخال سعر ابتدائي صحيح');
                    startingPrice.focus();
                    return false;
                }

                return true;
            }

            // Check for fast auction warning
            function checkFastAuction(duration) {
                const warningDiv = document.getElementById('fastAuctionWarning');
                if (parseInt(duration) < 15) {
                    warningDiv.style.display = 'block';
                } else {
                    warningDiv.style.display = 'none';
                }
            }

            // Add form validation on submit
            document.addEventListener('DOMContentLoaded', function() {
                const form = document.querySelector('form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        if (!validateForm()) {
                            e.preventDefault();
                        }
                    });
                }
            });
        </script>
    </body>
    </html>
    ''', available_numbers=available_numbers, selected_number=selected_number)

@app.route('/auction/details/<int:auction_id>')
def auction_details(auction_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    auction = Auction.query.get_or_404(auction_id)

    # Increment view count
    auction.views += 1

    # Check if current user is watching this auction
    user_id = session['user_id']
    is_watching = AuctionWatch.query.filter_by(
        auction_id=auction_id,
        customer_id=user_id
    ).first() is not None

    # Get all customers for bidding form
    customers = Customer.query.all()

    # Log activity
    activity = AuctionActivity(
        auction_id=auction_id,
        activity_type='view',
        details='عرض تفاصيل المزاد'
    )
    db.session.add(activity)
    db.session.commit()

    # Get bid history
    bid_history = Bid.query.filter_by(auction_id=auction_id).order_by(Bid.bid_time.desc()).all()

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تفاصيل المزاد - {{ auction.title }}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .auction-header {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border-radius: 15px;
                padding: 30px;
                margin-bottom: 30px;
            }
            .bid-item {
                border-left: 4px solid #007bff;
                padding: 10px 15px;
                margin-bottom: 10px;
                background: #f8f9fa;
                border-radius: 5px;
            }
            .bid-item.winning {
                border-left-color: #28a745;
                background: #d4edda;
            }
            .countdown-large {
                font-size: 2em;
                font-weight: bold;
                font-family: 'Courier New', monospace;
            }
            .watch-button {
                transition: all 0.3s ease;
                border-radius: 25px !important;
                font-weight: bold;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .watch-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }
            .watch-card {
                transition: all 0.3s ease;
                border-radius: 15px;
            }
            .watch-card:hover {
                transform: translateY(-2px);
            }
            .pulse-animation {
                animation: pulse-glow 2s infinite;
            }
            @keyframes pulse-glow {
                0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
                100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
            }
            .qatar-plate {
                background: #ffffff;
                border: 6px solid #000000;
                border-radius: 12px;
                padding: 20px 30px;
                margin: 20px auto;
                max-width: 450px;
                min-height: 120px;
                box-shadow: 0 8px 20px rgba(0,0,0,0.4);
                position: relative;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .plate-content {
                flex: 1;
                text-align: center;
                width: 100%;
            }
            .plate-number {
                font-family: 'Arial Black', Arial, sans-serif;
                font-size: 4em;
                font-weight: 900;
                color: #000000;
                margin: 0;
                letter-spacing: 12px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
                line-height: 1;
            }
            .plate-shine {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
                pointer-events: none;
                animation: shine 3s infinite;
            }
            @keyframes shine {
                0% { transform: translateX(-100%); }
                100% { transform: translateX(100%); }
            }
            .mini-qatar-plate {
                background: #ffffff;
                border: 3px solid #000000;
                border-radius: 6px;
                padding: 6px 12px;
                margin: 8px auto;
                max-width: 180px;
                min-height: 50px;
                box-shadow: 0 4px 10px rgba(0,0,0,0.3);
                position: relative;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .mini-plate-content {
                flex: 1;
                text-align: center;
                width: 100%;
            }
            .mini-plate-number {
                font-family: 'Arial Black', Arial, sans-serif;
                font-size: 1.3em;
                font-weight: 900;
                color: #000000;
                margin: 0;
                letter-spacing: 3px;
                line-height: 1;
            }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('auction') }}">المزاد</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Auction Header -->
            <div class="auction-header text-center">
                <h1><i class="fas fa-gavel"></i> {{ auction.title }}</h1>
                <p class="lead">{{ auction.description or 'مزاد رقم مميز' }}</p>

                <!-- Qatar License Plate -->
                <div class="qatar-plate">
                    <div class="plate-content">
                        <div class="plate-number">{{ auction.premium_number.number }}</div>
                    </div>
                    <div class="plate-shine"></div>
                </div>

                {% if auction.status == 'active' %}
                    <div class="countdown-large" id="countdown-main" data-end-time="{{ auction.end_time.isoformat() }}">
                        جاري التحميل...
                    </div>
                {% elif auction.status == 'scheduled' %}
                    <h4><i class="fas fa-clock"></i> يبدأ في: {{ auction.start_time.strftime('%Y-%m-%d %H:%M') }}</h4>
                {% else %}
                    <h4><i class="fas fa-flag-checkered"></i> انتهى المزاد</h4>
                {% endif %}
            </div>

            <div class="row">
                <!-- Main Auction Info -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-info-circle"></i> معلومات المزاد</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>الرقم المميز</h6>
                                    <p><strong>{{ auction.premium_number.number }}</strong></p>

                                    <h6>الفئة</h6>
                                    <p><span class="badge bg-warning">{{ auction.premium_number.category }}</span></p>

                                    <h6>السعر الابتدائي</h6>
                                    <p class="text-primary"><strong>{{ "{:,.0f}".format(auction.starting_price) }} ر.ق</strong></p>
                                </div>
                                <div class="col-md-6">
                                    <h6>الحد الأدنى للزيادة</h6>
                                    <p>{{ "{:,.0f}".format(auction.bid_increment) }} ر.ق</p>

                                    {% if auction.reserve_price %}
                                    <h6>السعر الاحتياطي</h6>
                                    <p>{{ "{:,.0f}".format(auction.reserve_price) }} ر.ق</p>
                                    {% endif %}

                                    <h6>إجمالي المزايدات</h6>
                                    <p><strong>{{ auction.total_bids }}</strong></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Winning Bid -->
                    {% set winning_bid = bid_history[0] if bid_history else None %}
                    {% if winning_bid %}
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-trophy"></i> المزايدة الرابحة حالياً</h5>
                        </div>
                        <div class="card-body text-center">
                            <h2 class="text-success">{{ "{:,.0f}".format(winning_bid.bid_amount) }} ر.ق</h2>
                            <p class="lead">{{ winning_bid.customer.name_ar }}</p>
                            <small class="text-muted">{{ winning_bid.bid_time.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Bidding Form -->
                    {% if auction.status == 'active' %}
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5><i class="fas fa-hand-paper"></i> تقديم مزايدة</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('place_bid_new', auction_id=auction.id) }}">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">العميل</label>
                                            <select class="form-select" name="customer_id" required>
                                                <option value="">اختر العميل</option>
                                                {% for customer in customers %}
                                                <option value="{{ customer.id }}">{{ customer.name_ar }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">مبلغ المزايدة</label>
                                            {% set min_bid = (winning_bid.bid_amount + auction.bid_increment) if winning_bid else auction.starting_price %}
                                            <input type="number" class="form-control" name="bid_amount"
                                                   step="{{ auction.bid_increment }}" min="{{ min_bid }}"
                                                   value="{{ min_bid }}" required>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-gavel"></i> تقديم مزايدة {{ "{:,.0f}".format(min_bid) }} ر.ق
                                </button>
                            </form>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Sidebar -->
                <div class="col-md-4">
                    <!-- Watch Button -->
                    <div class="card mb-4 border-{{ 'danger' if is_watching else 'primary' }} watch-card {{ 'pulse-animation' if not is_watching else '' }}" style="box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 15px;">
                        <div class="card-header bg-{{ 'danger' if is_watching else 'primary' }} text-white" style="background: linear-gradient(135deg, {{ '#dc3545, #c82333' if is_watching else '#007bff, #0056b3' }}); border-radius: 15px 15px 0 0;">
                            <h6 class="mb-0"><i class="fas fa-{{ 'bell-slash' if is_watching else 'bell' }}"></i>
                                {{ 'متابعة نشطة' if is_watching else 'تذكيرات المزاد' }}
                            </h6>
                        </div>
                        <div class="card-body text-center">
                            {% if is_watching %}
                                <div class="alert alert-success mb-3" style="border-radius: 10px;">
                                    <i class="fas fa-check-circle"></i> <strong>مُفعل!</strong> أنت تتابع هذا المزاد حالياً
                                </div>
                            {% else %}
                                <div class="alert alert-info mb-3" style="border-radius: 10px;">
                                    <i class="fas fa-info-circle"></i> احصل على تنبيهات فورية عند بدء المزاد والمزايدات الجديدة
                                </div>
                            {% endif %}
                            <button id="watchButton" class="btn btn-{{ 'outline-danger' if is_watching else 'primary' }} w-100 btn-lg watch-button"
                                    onclick="toggleWatch({{ auction.id }})">
                                <i class="fas fa-{{ 'bell-slash' if is_watching else 'bell' }}"></i>
                                {{ 'إلغاء التذكير' if is_watching else 'تفعيل التذكير' }}
                            </button>
                            <small class="text-muted d-block mt-3" style="line-height: 1.5;">
                                {% if is_watching %}
                                    <i class="fas fa-bell text-success"></i> <strong>نشط:</strong> ستتلقى إشعارات عن جميع التحديثات والمزايدات الجديدة
                                {% else %}
                                    <i class="fas fa-star text-warning"></i> <strong>مميز:</strong> تنبيهات فورية عبر النظام عند بدء المزاد والمزايدات المهمة
                                {% endif %}
                            </small>
                        </div>
                    </div>

                    <!-- Auction Stats -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h6><i class="fas fa-chart-bar"></i> إحصائيات المزاد</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <h4>{{ auction.total_bids }}</h4>
                                    <small>مزايدة</small>
                                </div>
                                <div class="col-6">
                                    <h4>{{ auction.views }}</h4>
                                    <small>مشاهدة</small>
                                </div>
                            </div>
                            <hr>
                            <div class="row text-center">
                                <div class="col-12">
                                    <h4>{{ auction.watchers|length }}</h4>
                                    <small>متابع</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bid History -->
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h6><i class="fas fa-history"></i> تاريخ المزايدات</h6>
                        </div>
                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                            {% if bid_history %}
                                {% for bid in bid_history %}
                                <div class="bid-item {% if loop.first %}winning{% endif %}">
                                    <div class="d-flex justify-content-between">
                                        <strong>{{ "{:,.0f}".format(bid.bid_amount) }} ر.ق</strong>
                                        {% if loop.first %}
                                            <span class="badge bg-success">رابحة</span>
                                        {% endif %}
                                    </div>
                                    <small>{{ bid.customer.name_ar }}</small><br>
                                    <small class="text-muted">{{ bid.bid_time.strftime('%H:%M:%S') }}</small>
                                </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted text-center">لا توجد مزايدات حتى الآن</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function updateCountdown() {
                const element = document.getElementById('countdown-main');
                if (!element) return;

                const endTime = new Date(element.dataset.endTime + 'Z').getTime();
                const now = new Date().getTime();
                const distance = endTime - now;

                if (distance > 0) {
                    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                    let timeString = '';
                    if (days > 0) timeString += days + ' يوم ';
                    timeString += String(hours).padStart(2, '0') + ':' +
                                 String(minutes).padStart(2, '0') + ':' +
                                 String(seconds).padStart(2, '0');

                    element.innerHTML = '<i class="fas fa-hourglass-half"></i> ' + timeString;

                    if (distance < 3600000) {
                        element.style.color = '#dc3545';
                    }
                } else {
                    element.innerHTML = '<i class="fas fa-flag-checkered"></i> انتهى المزاد';
                    setTimeout(() => location.reload(), 2000);
                }
            }

            setInterval(updateCountdown, 1000);
            updateCountdown();

            // Watch/Unwatch functionality
            function toggleWatch(auctionId) {
                const button = document.getElementById('watchButton');
                const originalText = button.innerHTML;

                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';

                fetch('/auction/watch/' + auctionId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const card = button.closest('.card');
                        const cardHeader = card.querySelector('.card-header');
                        const alertDiv = card.querySelector('.alert');
                        const smallText = button.nextElementSibling;

                        if (data.action === 'added') {
                            // Update to watching state
                            button.className = 'btn btn-outline-danger w-100 btn-lg watch-button';
                            button.innerHTML = '<i class="fas fa-bell-slash"></i> إلغاء التذكير';

                            card.className = 'card mb-4 border-danger watch-card';
                            card.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                            card.style.borderRadius = '15px';
                            cardHeader.className = 'card-header bg-danger text-white';
                            cardHeader.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
                            cardHeader.style.borderRadius = '15px 15px 0 0';
                            cardHeader.innerHTML = '<h6 class="mb-0"><i class="fas fa-bell-slash"></i> متابعة نشطة</h6>';

                            // Update alert
                            if (alertDiv) {
                                alertDiv.className = 'alert alert-success mb-3';
                                alertDiv.style.borderRadius = '10px';
                                alertDiv.innerHTML = '<i class="fas fa-check-circle"></i> <strong>مُفعل!</strong> أنت تتابع هذا المزاد حالياً';
                            } else {
                                const newAlert = document.createElement('div');
                                newAlert.className = 'alert alert-success mb-3';
                                newAlert.style.borderRadius = '10px';
                                newAlert.innerHTML = '<i class="fas fa-check-circle"></i> <strong>مُفعل!</strong> أنت تتابع هذا المزاد حالياً';
                                button.parentNode.insertBefore(newAlert, button);
                            }

                            smallText.innerHTML = '<i class="fas fa-bell text-success"></i> <strong>نشط:</strong> ستتلقى إشعارات عن جميع التحديثات والمزايدات الجديدة';
                            smallText.style.lineHeight = '1.5';
                        } else {
                            // Update to not watching state
                            button.className = 'btn btn-primary w-100 btn-lg watch-button';
                            button.innerHTML = '<i class="fas fa-bell"></i> تفعيل التذكير';

                            card.className = 'card mb-4 border-primary watch-card pulse-animation';
                            card.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                            card.style.borderRadius = '15px';
                            cardHeader.className = 'card-header bg-primary text-white';
                            cardHeader.style.background = 'linear-gradient(135deg, #007bff, #0056b3)';
                            cardHeader.style.borderRadius = '15px 15px 0 0';
                            cardHeader.innerHTML = '<h6 class="mb-0"><i class="fas fa-bell"></i> تذكيرات المزاد</h6>';

                            // Update alert
                            if (alertDiv) {
                                alertDiv.className = 'alert alert-info mb-3';
                                alertDiv.style.borderRadius = '10px';
                                alertDiv.innerHTML = '<i class="fas fa-info-circle"></i> احصل على تنبيهات فورية عند بدء المزاد والمزايدات الجديدة';
                            }

                            smallText.innerHTML = '<i class="fas fa-star text-warning"></i> <strong>مميز:</strong> تنبيهات فورية عبر النظام عند بدء المزاد والمزايدات المهمة';
                            smallText.style.lineHeight = '1.5';
                        }

                        // Show success message
                        showMessage(data.message, 'success');
                    } else {
                        showMessage(data.message, 'error');
                        button.innerHTML = originalText;
                    }
                })
                .catch(error => {
                    showMessage('حدث خطأ في الاتصال', 'error');
                    button.innerHTML = originalText;
                })
                .finally(() => {
                    button.disabled = false;
                });
            }

            function showMessage(message, type) {
                // Remove existing messages
                const existingMsg = document.querySelector('.alert-message');
                if (existingMsg) existingMsg.remove();

                // Create new message with icon
                const alertDiv = document.createElement('div');
                const icon = type === 'error' ? 'fas fa-exclamation-triangle' : 'fas fa-check-circle';
                alertDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show alert-message`;
                alertDiv.innerHTML = `
                    <i class="${icon}"></i> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                // Insert at top of container
                const container = document.querySelector('.container');
                container.insertBefore(alertDiv, container.firstChild);

                // Add animation effect
                alertDiv.style.transform = 'translateY(-20px)';
                alertDiv.style.opacity = '0';
                setTimeout(() => {
                    alertDiv.style.transition = 'all 0.3s ease';
                    alertDiv.style.transform = 'translateY(0)';
                    alertDiv.style.opacity = '1';
                }, 10);

                // Auto remove after 4 seconds
                setTimeout(() => {
                    if (alertDiv) {
                        alertDiv.style.transform = 'translateY(-20px)';
                        alertDiv.style.opacity = '0';
                        setTimeout(() => alertDiv.remove(), 300);
                    }
                }, 4000);
            }

            // Auto-refresh bid history every 10 seconds
            setInterval(() => {
                if (document.querySelector('#countdown-main')) {
                    location.reload();
                }
            }, 10000);

            // Contract form enhancements
            function showContractGuide() {
                const modal = new bootstrap.Modal(document.getElementById('contractGuideModal'));
                modal.show();
            }

            function updateStepIndicator(step) {
                // Reset all steps
                document.querySelectorAll('.step-indicator').forEach(el => {
                    el.classList.remove('active', 'completed');
                });

                // Mark completed steps
                for (let i = 1; i < step; i++) {
                    document.getElementById(`step${i}`).classList.add('completed');
                }

                // Mark current step
                document.getElementById(`step${step}`).classList.add('active');
            }

            // Update step indicator based on form progress
            function checkFormProgress() {
                const contractType = document.querySelector('input[name="contract_type"]:checked');
                const paymentType = document.querySelector('input[name="payment_type"]:checked');
                const totalAmount = document.getElementById('totalAmount').value;

                if (contractType) {
                    updateStepIndicator(2);
                    if (paymentType) {
                        updateStepIndicator(3);
                        if (totalAmount > 0) {
                            updateStepIndicator(4);
                        }
                    }
                } else {
                    updateStepIndicator(1);
                }
            }
        </script>
    </body>
    </html>
    ''', auction=auction, bid_history=bid_history, customers=customers, is_watching=is_watching)

@app.route('/auction_management')
def auction_management():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # Get all auctions
    all_auctions = Auction.query.order_by(Auction.created_at.desc()).all()

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إدارة المزادات - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('auction') }}">المزاد</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cogs"></i> إدارة المزادات</h2>
                <a href="{{ url_for('create_auction') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إنشاء مزاد جديد
                </a>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>المزاد</th>
                                    <th>الرقم المميز</th>
                                    <th>الحالة</th>
                                    <th>السعر الحالي</th>
                                    <th>المزايدات</th>
                                    <th>ينتهي في</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for auction in auctions %}
                                <tr>
                                    <td>
                                        <strong>{{ auction.title }}</strong><br>
                                        <small class="text-muted">#{{ auction.id }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ auction.premium_number.number }}</strong><br>
                                        <span class="badge bg-info">{{ auction.premium_number.category }}</span>
                                    </td>
                                    <td>
                                        {% if auction.status == 'active' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% elif auction.status == 'scheduled' %}
                                            <span class="badge bg-warning">مجدول</span>
                                        {% elif auction.status == 'ended' %}
                                            <span class="badge bg-secondary">منتهي</span>
                                        {% elif auction.status == 'cancelled' %}
                                            <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if auction.current_price > 0 %}
                                            <strong class="text-success">{{ "{:,.0f}".format(auction.current_price) }} ر.ق</strong>
                                        {% else %}
                                            <span class="text-muted">{{ "{:,.0f}".format(auction.starting_price) }} ر.ق</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ auction.total_bids }}</td>
                                    <td>
                                        {% if auction.status == 'active' %}
                                            {{ auction.end_time.strftime('%Y-%m-%d %H:%M') }}
                                        {% elif auction.status == 'scheduled' %}
                                            {{ auction.start_time.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('auction_details', auction_id=auction.id) }}" class="btn btn-sm btn-info" title="عرض تفاصيل المزاد">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                        {% if auction.status in ['scheduled', 'active'] %}
                                            <button class="btn btn-sm btn-warning" onclick="editAuction({{ auction.id }})"
                                                    title="{% if auction.total_bids > 0 %}تعديل محدود - يوجد مزايدات{% else %}تعديل كامل{% endif %}">
                                                <i class="fas fa-edit"></i> تعديل
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="cancelAuction({{ auction.id }})" title="إلغاء المزاد">
                                                <i class="fas fa-times"></i> إلغاء
                                            </button>
                                        {% else %}
                                            <span class="badge bg-secondary">غير قابل للتعديل</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Auction Modal -->
        <div class="modal fade" id="editAuctionModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">تعديل المزاد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info" id="editWarning" style="display: none;">
                            <i class="fas fa-info-circle"></i>
                            <strong>تنبيه:</strong> <span id="editWarningText"></span>
                        </div>
                        <form id="editAuctionForm">
                            <input type="hidden" id="editAuctionId">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">عنوان المزاد</label>
                                    <input type="text" class="form-control" id="editTitle" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">السعر الابتدائي (ر.ق)</label>
                                    <input type="number" class="form-control" id="editStartingPrice" step="0.01" required>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label class="form-label">السعر الاحتياطي (ر.ق)</label>
                                    <input type="number" class="form-control" id="editReservePrice" step="0.01">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">زيادة المزايدة (ر.ق)</label>
                                    <input type="number" class="form-control" id="editBidIncrement" step="0.01" required>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label class="form-label">تاريخ البداية</label>
                                    <input type="datetime-local" class="form-control" id="editStartTime" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">تاريخ النهاية</label>
                                    <input type="datetime-local" class="form-control" id="editEndTime" required>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <label class="form-label">وصف المزاد</label>
                                    <textarea class="form-control" id="editDescription" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="editAutoExtend">
                                        <label class="form-check-label" for="editAutoExtend">
                                            تمديد تلقائي للمزاد
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-warning" onclick="saveAuctionChanges()">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function editAuction(auctionId) {
                // Fetch auction details
                fetch('/auction/edit/' + auctionId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const auction = data.auction;

                        // Fill form fields
                        document.getElementById('editAuctionId').value = auction.id;
                        document.getElementById('editTitle').value = auction.title;
                        document.getElementById('editStartingPrice').value = auction.starting_price;
                        document.getElementById('editReservePrice').value = auction.reserve_price || '';
                        document.getElementById('editBidIncrement').value = auction.bid_increment;
                        document.getElementById('editDescription').value = auction.description || '';
                        document.getElementById('editAutoExtend').checked = auction.auto_extend;

                        // Format datetime for input fields
                        const startTime = new Date(auction.start_time).toISOString().slice(0, 16);
                        const endTime = new Date(auction.end_time).toISOString().slice(0, 16);
                        document.getElementById('editStartTime').value = startTime;
                        document.getElementById('editEndTime').value = endTime;

                        // Show appropriate warnings and disable fields
                        const warningDiv = document.getElementById('editWarning');
                        const warningText = document.getElementById('editWarningText');

                        if (auction.status === 'active') {
                            warningDiv.style.display = 'block';
                            warningText.textContent = 'المزاد نشط حالياً. لا يمكن تعديل الأسعار أو التوقيتات.';
                            document.getElementById('editStartingPrice').disabled = true;
                            document.getElementById('editReservePrice').disabled = true;
                            document.getElementById('editStartTime').disabled = true;
                            document.getElementById('editEndTime').disabled = true;
                        } else if (auction.status === 'scheduled') {
                            warningDiv.style.display = 'block';
                            warningText.textContent = 'يمكن تعديل جميع الحقول قبل بدء المزاد.';
                            // Enable all fields
                            document.getElementById('editStartingPrice').disabled = false;
                            document.getElementById('editReservePrice').disabled = false;
                            document.getElementById('editStartTime').disabled = false;
                            document.getElementById('editEndTime').disabled = false;
                        }

                        // Show modal
                        new bootstrap.Modal(document.getElementById('editAuctionModal')).show();
                    } else {
                        alert('حدث خطأ في تحميل بيانات المزاد: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                });
            }

            function saveAuctionChanges() {
                const auctionId = document.getElementById('editAuctionId').value;
                const formData = {
                    title: document.getElementById('editTitle').value,
                    starting_price: parseFloat(document.getElementById('editStartingPrice').value),
                    reserve_price: document.getElementById('editReservePrice').value ? parseFloat(document.getElementById('editReservePrice').value) : null,
                    bid_increment: parseFloat(document.getElementById('editBidIncrement').value),
                    start_time: document.getElementById('editStartTime').value,
                    end_time: document.getElementById('editEndTime').value,
                    description: document.getElementById('editDescription').value,
                    auto_extend: document.getElementById('editAutoExtend').checked
                };

                fetch('/auction/edit/' + auctionId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم تحديث المزاد بنجاح!');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                });
            }

            function cancelAuction(auctionId) {
                if (confirm('هل أنت متأكد من إلغاء هذا المزاد؟')) {
                    fetch('/auction/cancel/' + auctionId, {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.message);
                        }
                    });
                }
            }
        </script>
    </body>
    </html>
    ''', auctions=all_auctions)

@app.route('/auction/cancel/<int:auction_id>', methods=['POST'])
def cancel_auction(auction_id):
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})

    try:
        auction = Auction.query.get_or_404(auction_id)

        # Check if auction can be cancelled
        if auction.status not in ['scheduled', 'active']:
            return jsonify({'success': False, 'message': 'لا يمكن إلغاء هذا المزاد'})

        # Update auction status
        auction.status = 'cancelled'

        # Update premium number status back to available
        if auction.premium_number:
            auction.premium_number.status = 'available'

        db.session.commit()

        return jsonify({'success': True, 'message': 'تم إلغاء المزاد بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/auction/edit/<int:auction_id>', methods=['GET', 'POST'])
def edit_auction(auction_id):
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})

    try:
        auction = Auction.query.get_or_404(auction_id)

        if request.method == 'GET':
            # Return auction data for editing
            return jsonify({
                'success': True,
                'auction': {
                    'id': auction.id,
                    'title': auction.title,
                    'description': auction.description,
                    'starting_price': float(auction.starting_price),
                    'reserve_price': float(auction.reserve_price) if auction.reserve_price else None,
                    'bid_increment': float(auction.bid_increment),
                    'start_time': auction.start_time.isoformat(),
                    'end_time': auction.end_time.isoformat(),
                    'auto_extend': auction.auto_extend,
                    'status': auction.status
                }
            })

        elif request.method == 'POST':
            # Update auction
            data = request.get_json()

            # Check if auction can be edited
            if auction.status not in ['scheduled', 'active']:
                return jsonify({'success': False, 'message': 'لا يمكن تعديل هذا المزاد'})

            # Update fields
            auction.title = data.get('title', auction.title)
            auction.description = data.get('description', auction.description)
            auction.bid_increment = data.get('bid_increment', auction.bid_increment)
            auction.auto_extend = data.get('auto_extend', auction.auto_extend)

            # Only update prices if no bids have been placed
            if auction.total_bids == 0:
                auction.starting_price = data.get('starting_price', auction.starting_price)
                auction.reserve_price = data.get('reserve_price', auction.reserve_price)

            # Update times only if auction hasn't started
            if auction.status == 'scheduled':
                from datetime import datetime
                start_time_str = data.get('start_time')
                end_time_str = data.get('end_time')

                if start_time_str:
                    auction.start_time = datetime.fromisoformat(start_time_str.replace('T', ' '))
                if end_time_str:
                    auction.end_time = datetime.fromisoformat(end_time_str.replace('T', ' '))

            db.session.commit()

            return jsonify({'success': True, 'message': 'تم تحديث المزاد بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/notifications')
def notifications():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']

    # Get all notifications for the user
    notifications = Notification.query.filter_by(user_id=user_id).order_by(Notification.created_at.desc()).all()

    # Mark all as read
    Notification.query.filter_by(user_id=user_id, is_read=False).update({'is_read': True})
    db.session.commit()

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>الإشعارات - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .notification-item {
                border-left: 4px solid #007bff;
                transition: all 0.3s ease;
            }
            .notification-item:hover {
                background-color: #f8f9fa;
                transform: translateX(-5px);
            }
            .notification-item.auction-start {
                border-left-color: #28a745;
            }
            .notification-item.new-bid {
                border-left-color: #ffc107;
            }
            .notification-item.auction-end {
                border-left-color: #dc3545;
            }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('auction') }}">المزاد</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bell"></i> الإشعارات</h2>
                <a href="{{ url_for('auction') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right"></i> العودة للمزاد
                </a>
            </div>

            {% if notifications %}
                <div class="row">
                    {% for notification in notifications %}
                    <div class="col-12 mb-3">
                        <div class="card notification-item {{ notification.notification_type }}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="card-title">
                                            {% if notification.notification_type == 'auction_start' %}
                                                <i class="fas fa-play-circle text-success"></i>
                                            {% elif notification.notification_type == 'new_bid' %}
                                                <i class="fas fa-hand-paper text-warning"></i>
                                            {% elif notification.notification_type == 'auction_end' %}
                                                <i class="fas fa-flag-checkered text-danger"></i>
                                            {% endif %}
                                            {{ notification.title }}
                                        </h6>
                                        <p class="card-text">{{ notification.message }}</p>
                                        {% if notification.auction %}
                                            <a href="{{ url_for('auction_details', auction_id=notification.auction.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> عرض المزاد
                                            </a>
                                        {% endif %}
                                    </div>
                                    <small class="text-muted">{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h4>لا توجد إشعارات</h4>
                    <p class="text-muted">ستظهر هنا إشعارات المزادات التي تتابعها</p>
                    <a href="{{ url_for('auction') }}" class="btn btn-primary">
                        <i class="fas fa-gavel"></i> تصفح المزادات
                    </a>
                </div>
            {% endif %}
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''', notifications=notifications)

@app.route('/premium_numbers/bulk_auction', methods=['POST'])
def bulk_create_auctions():
    """إنشاء مزادات متعددة للأرقام المحددة"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})

    try:
        data = request.get_json()
        number_ids = data.get('number_ids', [])
        auction_settings = data.get('settings', {})

        if not number_ids:
            return jsonify({'success': False, 'message': 'لم يتم تحديد أرقام'})

        created_auctions = []

        for number_id in number_ids:
            number = PremiumNumber.query.get(number_id)
            if number and number.status == 'available':
                from datetime import datetime, timedelta

                # Create auction
                auction = Auction(
                    premium_number_id=number_id,
                    title=f"مزاد الرقم المميز {number.number}",
                    description=f"مزاد علني للرقم المميز {number.number} من فئة {number.category}",
                    starting_price=auction_settings.get('starting_price', number.price),
                    reserve_price=auction_settings.get('reserve_price'),
                    bid_increment=auction_settings.get('bid_increment', 1000),
                    start_time=datetime.utcnow() + timedelta(minutes=5),
                    end_time=datetime.utcnow() + timedelta(minutes=auction_settings.get('duration_minutes', 1440)),
                    auto_extend=auction_settings.get('auto_extend', True),
                    created_by=session['user_id']
                )

                db.session.add(auction)

                # Update number status
                number.status = 'auction'

                created_auctions.append(auction)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'تم إنشاء {len(created_auctions)} مزاد بنجاح',
            'count': len(created_auctions)
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

# ==================== لوحة التحكم الإدارية ====================
@app.route('/admin')
def admin_panel():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # Get statistics
    stats = {
        'total_cars': Car.query.count(),
        'available_cars': Car.query.filter_by(status='available').count(),
        'sold_cars': Car.query.filter_by(status='sold').count(),
        'total_numbers': PremiumNumber.query.count(),
        'available_numbers': PremiumNumber.query.filter_by(status='available').count(),
        'sold_numbers': PremiumNumber.query.filter_by(status='sold').count(),
        'total_customers': Customer.query.count(),
        'total_contracts': Contract.query.count(),
        'total_bids': Bid.query.count(),
        'active_bids': Bid.query.filter_by(status='active').count(),
    }

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>لوحة التحكم الإدارية - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .stat-card { transition: transform 0.3s; }
            .stat-card:hover { transform: translateY(-5px); }
            .admin-section { border: 2px solid #e9ecef; border-radius: 15px; padding: 20px; margin: 15px 0; }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات - لوحة التحكم
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    <a class="nav-link" href="{{ url_for('logout') }}">خروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم الإدارية</h2>

            <!-- Statistics -->
            <div class="admin-section">
                <h4><i class="fas fa-chart-bar"></i> الإحصائيات العامة</h4>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-car fa-2x mb-2"></i>
                                <h4>{{ stats.total_cars }}</h4>
                                <p>إجمالي السيارات</p>
                                <small>متاح: {{ stats.available_cars }} | مباع: {{ stats.sold_cars }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-hashtag fa-2x mb-2"></i>
                                <h4>{{ stats.total_numbers }}</h4>
                                <p>الأرقام المميزة</p>
                                <small>متاح: {{ stats.available_numbers }} | مباع: {{ stats.sold_numbers }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h4>{{ stats.total_customers }}</h4>
                                <p>العملاء</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-file-contract fa-2x mb-2"></i>
                                <h4>{{ stats.total_contracts }}</h4>
                                <p>العقود</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="admin-section">
                <h4><i class="fas fa-bolt"></i> الإجراءات السريعة</h4>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('add_car') }}" class="btn btn-primary w-100 p-3">
                            <i class="fas fa-plus fa-2x d-block mb-2"></i>
                            إضافة سيارة جديدة
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('add_premium_number') }}" class="btn btn-success w-100 p-3">
                            <i class="fas fa-hashtag fa-2x d-block mb-2"></i>
                            إضافة رقم مميز
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('add_customer') }}" class="btn btn-info w-100 p-3">
                            <i class="fas fa-user-plus fa-2x d-block mb-2"></i>
                            إضافة عميل جديد
                        </a>
                    </div>
                </div>
            </div>

            <!-- Management Links -->
            <div class="admin-section">
                <h4><i class="fas fa-cogs"></i> إدارة الأقسام</h4>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="list-group">
                            <a href="{{ url_for('cars') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-car"></i> إدارة السيارات
                            </a>
                            <a href="{{ url_for('premium_numbers') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-hashtag"></i> إدارة الأرقام المميزة
                            </a>
                            <a href="{{ url_for('customers') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-users"></i> إدارة العملاء
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="list-group">
                            <a href="{{ url_for('contracts') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-file-contract"></i> إدارة العقود
                            </a>
                            <a href="{{ url_for('auction') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-gavel"></i> نظام المزاد
                            </a>
                            <a href="{{ url_for('reports') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-chart-line"></i> التقارير والإحصائيات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', stats=stats)

# ==================== التقارير والإحصائيات ====================
@app.route('/reports')
def reports():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # Calculate detailed statistics
    total_revenue = db.session.query(db.func.sum(Contract.total_amount)).scalar() or 0
    car_sales_revenue = db.session.query(db.func.sum(Contract.total_amount)).filter(
        Contract.contract_type.in_(['car_sale', 'combined_sale'])
    ).scalar() or 0
    number_sales_revenue = db.session.query(db.func.sum(Contract.total_amount)).filter(
        Contract.contract_type.in_(['premium_number_sale', 'combined_sale'])
    ).scalar() or 0

    # Recent contracts
    recent_contracts = Contract.query.order_by(Contract.created_at.desc()).limit(10).all()

    # Top customers by spending
    top_customers = db.session.query(
        Customer.name_ar,
        db.func.sum(Contract.total_amount).label('total_spent'),
        db.func.count(Contract.id).label('contract_count')
    ).join(Contract).group_by(Customer.id).order_by(db.desc('total_spent')).limit(5).all()

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>التقارير والإحصائيات - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .report-card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .revenue-card { background: linear-gradient(135deg, #28a745, #20c997); color: white; }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    <a class="nav-link" href="{{ url_for('admin_panel') }}">لوحة التحكم</a>
                    <a class="nav-link" href="{{ url_for('logout') }}">خروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <h2><i class="fas fa-chart-line"></i> التقارير والإحصائيات</h2>

            <!-- Revenue Statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card report-card revenue-card">
                        <div class="card-body text-center">
                            <i class="fas fa-money-bill-wave fa-3x mb-3"></i>
                            <h3>{{ "{:,.0f}".format(total_revenue) }} ر.ق</h3>
                            <p class="mb-0">إجمالي الإيرادات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card report-card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-car fa-3x mb-3"></i>
                            <h3>{{ "{:,.0f}".format(car_sales_revenue) }} ر.ق</h3>
                            <p class="mb-0">مبيعات السيارات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card report-card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-hashtag fa-3x mb-3"></i>
                            <h3>{{ "{:,.0f}".format(number_sales_revenue) }} ر.ق</h3>
                            <p class="mb-0">مبيعات الأرقام المميزة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Contracts -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card report-card">
                        <div class="card-header bg-info text-white">
                            <h5><i class="fas fa-clock"></i> أحدث العقود</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>رقم العقد</th>
                                            <th>النوع</th>
                                            <th>العميل</th>
                                            <th>المبلغ</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for contract in recent_contracts %}
                                        <tr>
                                            <td>{{ contract.contract_number }}</td>
                                            <td>
                                                {% if contract.contract_type == 'car_sale' %}
                                                    <span class="badge bg-primary">سيارة</span>
                                                {% elif contract.contract_type == 'premium_number_sale' %}
                                                    <span class="badge bg-success">رقم مميز</span>
                                                {% else %}
                                                    <span class="badge bg-info">مدمج</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ contract.customer.name_ar }}</td>
                                            <td>{{ "{:,.0f}".format(contract.total_amount) }} ر.ق</td>
                                            <td>{{ contract.created_at.strftime('%Y-%m-%d') }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Customers -->
                <div class="col-md-4">
                    <div class="card report-card">
                        <div class="card-header bg-warning text-dark">
                            <h5><i class="fas fa-crown"></i> أفضل العملاء</h5>
                        </div>
                        <div class="card-body">
                            {% for customer in top_customers %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong>{{ customer.name_ar }}</strong><br>
                                    <small>{{ customer.contract_count }} عقد</small>
                                </div>
                                <div class="text-end">
                                    <strong>{{ "{:,.0f}".format(customer.total_spent) }} ر.ق</strong>
                                </div>
                            </div>
                            <hr>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''',
    total_revenue=total_revenue,
    car_sales_revenue=car_sales_revenue,
    number_sales_revenue=number_sales_revenue,
    recent_contracts=recent_contracts,
    top_customers=top_customers)

@app.route('/contracts/add', methods=['GET', 'POST'])
def add_contract():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    if request.method == 'POST':
        try:
            from datetime import datetime, timedelta

            # Get basic form data
            contract_type = request.form.get('contract_type')
            payment_type = request.form.get('payment_type')
            customer_id = request.form.get('customer_id', type=int)
            car_id = request.form.get('car_id', type=int) if request.form.get('car_id') else None
            premium_number_id = request.form.get('premium_number_id', type=int) if request.form.get('premium_number_id') else None

            # Financial details
            total_amount = request.form.get('total_amount', type=float)
            down_payment = request.form.get('down_payment', type=float) or 0
            monthly_payment = request.form.get('monthly_payment', type=float) or 0
            installment_months = request.form.get('installment_months', type=int) or 0
            interest_rate = request.form.get('interest_rate', type=float) or 0
            discount_amount = request.form.get('discount_amount', type=float) or 0
            tax_amount = request.form.get('tax_amount', type=float) or 0

            # Contract details
            contract_date = datetime.strptime(request.form.get('contract_date'), '%Y-%m-%d').date() if request.form.get('contract_date') else datetime.now().date()
            delivery_date = datetime.strptime(request.form.get('delivery_date'), '%Y-%m-%d').date() if request.form.get('delivery_date') else None
            warranty_months = request.form.get('warranty_months', type=int) or 0
            insurance_required = 'insurance_required' in request.form
            registration_included = 'registration_included' in request.form

            # Additional info
            special_conditions = request.form.get('special_conditions', '')
            notes = request.form.get('notes', '')
            witness_name = request.form.get('witness_name', '')
            witness_id = request.form.get('witness_id', '')

            # Validation
            if not all([contract_type, payment_type, customer_id, total_amount]):
                flash('جميع الحقول المطلوبة يجب ملؤها', 'error')
                return redirect(url_for('add_contract'))

            # Validate required items based on contract type
            if contract_type == 'car_sale' and not car_id:
                flash('يجب اختيار سيارة لعقد بيع السيارة', 'error')
                return redirect(url_for('add_contract'))
            elif contract_type == 'car_purchase' and not car_id:
                flash('يجب اختيار سيارة لعقد شراء السيارة', 'error')
                return redirect(url_for('add_contract'))
            elif contract_type == 'premium_number_sale' and not premium_number_id:
                flash('يجب اختيار رقم مميز لعقد بيع الرقم المميز', 'error')
                return redirect(url_for('add_contract'))
            elif contract_type == 'installment' and installment_months == 0:
                flash('يجب تحديد عدد الأقساط لعقد التقسيط', 'error')
                return redirect(url_for('add_contract'))

            # Calculate remaining amount
            remaining_amount = total_amount - down_payment - discount_amount + tax_amount

            # Create contract
            contract = Contract(
                contract_number=generate_contract_number(),
                contract_type=contract_type,
                payment_type=payment_type,
                customer_id=customer_id,
                car_id=car_id,
                premium_number_id=premium_number_id,
                total_amount=total_amount,
                down_payment=down_payment,
                remaining_amount=remaining_amount,
                monthly_payment=monthly_payment,
                installment_months=installment_months,
                interest_rate=interest_rate,
                contract_date=contract_date,
                delivery_date=delivery_date,
                warranty_months=warranty_months,
                insurance_required=insurance_required,
                registration_included=registration_included,
                special_conditions=special_conditions,
                notes=notes,
                discount_amount=discount_amount,
                tax_amount=tax_amount,
                witness_name=witness_name,
                witness_id=witness_id,
                status='active',
                created_by=session['user_id']
            )

            db.session.add(contract)
            db.session.flush()  # This will assign an ID to the contract

            # Create installment schedule if payment type is installment
            if payment_type == 'installment' and installment_months > 0:
                for i in range(1, installment_months + 1):
                    due_date = contract_date + timedelta(days=30 * i)
                    installment = InstallmentPayment(
                        contract_id=contract.id,
                        installment_number=i,
                        due_date=due_date,
                        amount=monthly_payment,
                        status='pending'
                    )
                    db.session.add(installment)

            # Update car status if needed
            if car_id and contract_type in ['car_sale']:
                car = Car.query.get(car_id)
                if car:
                    car.status = 'sold'
            elif car_id and contract_type in ['car_purchase']:
                car = Car.query.get(car_id)
                if car:
                    car.status = 'available'  # Car purchased by dealer

            # Update premium number status if needed
            if premium_number_id and contract_type in ['premium_number_sale']:
                premium_number = PremiumNumber.query.get(premium_number_id)
                if premium_number:
                    premium_number.status = 'sold'

            db.session.commit()

            flash(f'تم إنشاء العقد {contract.contract_number} بنجاح', 'success')
            return redirect(url_for('view_contract', contract_id=contract.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء العقد: {str(e)}', 'error')
            return redirect(url_for('add_contract'))

    # GET request - show form
    customers = Customer.query.all()
    cars = Car.query.filter_by(status='available').all()
    premium_numbers = PremiumNumber.query.filter_by(status='available').all()
    from datetime import datetime as dt
    today = dt.now().strftime('%Y-%m-%d')

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إضافة عقد جديد - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .contract-section { border: 2px solid #e9ecef; border-radius: 10px; padding: 20px; margin: 15px 0; }
            .section-header { background: #f8f9fa; margin: -20px -20px 15px -20px; padding: 15px 20px; border-radius: 8px 8px 0 0; }
            .feature-highlight { background: linear-gradient(135deg, #28a745, #20c997); color: white; }

            .step-indicator {
                padding: 15px;
                border-radius: 10px;
                transition: all 0.3s;
                border: 2px solid #e9ecef;
                background: #f8f9fa;
                color: #6c757d;
            }
            .step-indicator.active {
                background: #007bff;
                color: white;
                border-color: #007bff;
                transform: scale(1.05);
            }
            .step-indicator.completed {
                background: #28a745;
                color: white;
                border-color: #28a745;
            }
            .step-indicator i {
                font-size: 1.5em;
                margin-bottom: 5px;
            }
            .step-indicator p {
                margin: 0;
                font-size: 0.9em;
                font-weight: bold;
            }

            .contract-preview {
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                border-radius: 15px;
                padding: 20px;
                margin: 20px 0;
                border: 2px dashed #007bff;
            }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('contracts') }}">العقود</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Success Banner -->
            <div class="contract-section feature-highlight">
                <h4><i class="fas fa-star"></i> الميزة الجديدة: دعم الأرقام المميزة في العقود</h4>
                <p class="mb-0">✅ يمكنك الآن إنشاء عقود لبيع السيارات، الأرقام المميزة، أو كليهما معاً</p>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-plus"></i> إضافة عقد جديد</h2>
                <div>
                    <a href="{{ url_for('contracts') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للعقود
                    </a>
                    <button type="button" class="btn btn-info" onclick="showContractGuide()">
                        <i class="fas fa-question-circle"></i> دليل الاستخدام
                    </button>
                </div>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Progress Indicator -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="step-indicator active" id="step1">
                                <i class="fas fa-file-contract"></i>
                                <p>نوع العقد</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="step-indicator" id="step2">
                                <i class="fas fa-credit-card"></i>
                                <p>طريقة الدفع</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="step-indicator" id="step3">
                                <i class="fas fa-calculator"></i>
                                <p>التفاصيل المالية</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="step-indicator" id="step4">
                                <i class="fas fa-check"></i>
                                <p>المراجعة والحفظ</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form method="POST" id="contractForm">
                <!-- Contract Type Section -->
                <div class="contract-section">
                    <div class="section-header">
                        <h5><i class="fas fa-file-contract"></i> نوع العقد</h5>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="contract_type" value="car_sale" id="car_sale" onchange="updateContractType()">
                                <label class="form-check-label" for="car_sale">
                                    <i class="fas fa-car text-primary"></i> بيع سيارة
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="contract_type" value="car_purchase" id="car_purchase" onchange="updateContractType()">
                                <label class="form-check-label" for="car_purchase">
                                    <i class="fas fa-shopping-cart text-warning"></i> شراء سيارة
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="contract_type" value="premium_number_sale" id="premium_number_sale" onchange="updateContractType()">
                                <label class="form-check-label" for="premium_number_sale">
                                    <i class="fas fa-hashtag text-success"></i> بيع رقم مميز
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="contract_type" value="installment" id="installment_contract" onchange="updateContractType()">
                                <label class="form-check-label" for="installment_contract">
                                    <i class="fas fa-calendar-alt text-info"></i> عقد تقسيط
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Type Section -->
                <div class="contract-section">
                    <div class="section-header">
                        <h5><i class="fas fa-credit-card"></i> طريقة الدفع</h5>
                    </div>
                    <div class="row">
                        <div class="col-md-2">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_type" value="cash" id="cash" onchange="updatePaymentType()">
                                <label class="form-check-label" for="cash">
                                    <i class="fas fa-money-bill text-success"></i> نقدي
                                </label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_type" value="installment" id="installment" onchange="updatePaymentType()">
                                <label class="form-check-label" for="installment">
                                    <i class="fas fa-calendar-alt text-warning"></i> تقسيط
                                </label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_type" value="bank_finance" id="bank_finance" onchange="updatePaymentType()">
                                <label class="form-check-label" for="bank_finance">
                                    <i class="fas fa-university text-primary"></i> تمويل بنكي
                                </label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_type" value="lease" id="lease" onchange="updatePaymentType()">
                                <label class="form-check-label" for="lease">
                                    <i class="fas fa-handshake text-info"></i> إيجار
                                </label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_type" value="trade_in" id="trade_in" onchange="updatePaymentType()">
                                <label class="form-check-label" for="trade_in">
                                    <i class="fas fa-exchange-alt text-secondary"></i> استبدال
                                </label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_type" value="mixed" id="mixed" onchange="updatePaymentType()">
                                <label class="form-check-label" for="mixed">
                                    <i class="fas fa-coins text-dark"></i> مختلط
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Section -->
                <div class="contract-section">
                    <div class="section-header">
                        <h5><i class="fas fa-user"></i> العميل</h5>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <select class="form-select" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}">{{ customer.name_ar }} - {{ customer.phone }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Car Selection Section -->
                <div class="contract-section" id="carSection" style="display: none;">
                    <div class="section-header">
                        <h5><i class="fas fa-car"></i> اختيار السيارة</h5>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <select class="form-select" name="car_id" id="carSelect" onchange="updateTotalAmount()">
                                <option value="">اختر السيارة</option>
                                {% for car in cars %}
                                <option value="{{ car.id }}" data-price="{{ car.price }}">
                                    {{ car.make }} {{ car.model }} {{ car.year }} - {{ "{:,.0f}".format(car.price) }} ر.ق
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Premium Number Selection Section -->
                <div class="contract-section" id="premiumNumberSection" style="display: none;">
                    <div class="section-header">
                        <h5><i class="fas fa-hashtag"></i> اختيار الرقم المميز</h5>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <select class="form-select" name="premium_number_id" id="premiumNumberSelect" onchange="updateTotalAmount()">
                                <option value="">اختر الرقم المميز</option>
                                {% for number in premium_numbers %}
                                <option value="{{ number.id }}" data-price="{{ number.price }}">
                                    {{ number.number }} - {{ number.category }} - {{ "{:,.0f}".format(number.price) }} ر.ق
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Financial Details Section -->
                <div class="contract-section">
                    <div class="section-header">
                        <h5><i class="fas fa-calculator"></i> التفاصيل المالية</h5>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">المبلغ الأساسي (ر.ق)</label>
                            <input type="number" class="form-control" name="base_amount" id="baseAmount" step="0.01" readonly>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">مبلغ الخصم (ر.ق)</label>
                            <input type="number" class="form-control" name="discount_amount" id="discountAmount" step="0.01" min="0" value="0" onchange="calculateTotal()">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">مبلغ الضريبة (ر.ق)</label>
                            <input type="number" class="form-control" name="tax_amount" id="taxAmount" step="0.01" min="0" value="0" onchange="calculateTotal()">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label"><strong>المبلغ الإجمالي (ر.ق)</strong></label>
                            <input type="number" class="form-control bg-light" name="total_amount" id="totalAmount" step="0.01" required readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الدفعة المقدمة (ر.ق)</label>
                            <input type="number" class="form-control" name="down_payment" id="downPayment" step="0.01" min="0" value="0" onchange="calculateRemaining()">
                        </div>
                    </div>
                </div>

                <!-- Installment Details Section -->
                <div class="contract-section" id="installmentSection" style="display: none;">
                    <div class="section-header bg-warning text-dark">
                        <h5><i class="fas fa-calendar-check"></i> تفاصيل التقسيط</h5>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">عدد الأقساط (شهر)</label>
                            <select class="form-select" name="installment_months" id="installmentMonths" onchange="calculateInstallment()">
                                <option value="0">اختر عدد الأقساط</option>
                                <option value="6">6 أشهر</option>
                                <option value="12">12 شهر</option>
                                <option value="18">18 شهر</option>
                                <option value="24">24 شهر</option>
                                <option value="36">36 شهر</option>
                                <option value="48">48 شهر</option>
                                <option value="60">60 شهر</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">معدل الفائدة السنوي (%)</label>
                            <input type="number" class="form-control" name="interest_rate" id="interestRate" step="0.1" min="0" max="50" value="0" onchange="calculateInstallment()">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">القسط الشهري (ر.ق)</label>
                            <input type="number" class="form-control bg-light" name="monthly_payment" id="monthlyPayment" step="0.01" readonly>
                        </div>
                    </div>
                </div>

                <!-- Contract Details Section -->
                <div class="contract-section">
                    <div class="section-header bg-info text-white">
                        <h5><i class="fas fa-file-alt"></i> تفاصيل العقد</h5>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">تاريخ العقد</label>
                            <input type="date" class="form-control" name="contract_date" value="{{ today }}" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">تاريخ التسليم</label>
                            <input type="date" class="form-control" name="delivery_date">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">فترة الضمان (شهر)</label>
                            <select class="form-select" name="warranty_months">
                                <option value="0">بدون ضمان</option>
                                <option value="6">6 أشهر</option>
                                <option value="12" selected>12 شهر</option>
                                <option value="24">24 شهر</option>
                                <option value="36">36 شهر</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="insurance_required" id="insurance_required">
                                <label class="form-check-label" for="insurance_required">
                                    <i class="fas fa-shield-alt"></i> التأمين مطلوب
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="registration_included" id="registration_included">
                                <label class="form-check-label" for="registration_included">
                                    <i class="fas fa-id-card"></i> الترخيص مشمول
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Witness and Additional Info Section -->
                <div class="contract-section">
                    <div class="section-header bg-secondary text-white">
                        <h5><i class="fas fa-user-friends"></i> الشاهد والمعلومات الإضافية</h5>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">اسم الشاهد</label>
                            <input type="text" class="form-control" name="witness_name" placeholder="اسم الشاهد الكامل">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم هوية الشاهد</label>
                            <input type="text" class="form-control" name="witness_id" placeholder="رقم الهوية أو الإقامة">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">الشروط الخاصة</label>
                            <textarea class="form-control" name="special_conditions" rows="3" placeholder="أي شروط خاصة للعقد..."></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Summary Section -->
                <div class="contract-section" id="summarySection" style="display: none;">
                    <div class="section-header bg-info text-white">
                        <h5><i class="fas fa-eye"></i> ملخص العقد</h5>
                    </div>
                    <div id="contractSummary"></div>
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fas fa-save"></i> إنشاء العقد
                    </button>
                    <a href="{{ url_for('contracts') }}" class="btn btn-secondary btn-lg ms-2">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>

        <script>
            function updateContractType() {
                const contractType = document.querySelector('input[name="contract_type"]:checked')?.value;
                const carSection = document.getElementById('carSection');
                const premiumNumberSection = document.getElementById('premiumNumberSection');
                const carSelect = document.getElementById('carSelect');
                const premiumNumberSelect = document.getElementById('premiumNumberSelect');

                // Hide all sections first
                carSection.style.display = 'none';
                premiumNumberSection.style.display = 'none';

                // Clear selections
                carSelect.value = '';
                premiumNumberSelect.value = '';

                // Show relevant sections based on contract type
                if (contractType === 'car_sale' || contractType === 'car_purchase' || contractType === 'installment') {
                    carSection.style.display = 'block';
                }
                if (contractType === 'premium_number_sale') {
                    premiumNumberSection.style.display = 'block';
                }

                updateTotalAmount();
                updateSummary();
                checkFormProgress();
            }

            function updatePaymentType() {
                const paymentType = document.querySelector('input[name="payment_type"]:checked')?.value;
                const installmentSection = document.getElementById('installmentSection');

                // Show installment section for installment and bank_finance
                if (paymentType === 'installment' || paymentType === 'bank_finance') {
                    installmentSection.style.display = 'block';
                } else {
                    installmentSection.style.display = 'none';
                }

                updateSummary();
                checkFormProgress();
            }

            function updateTotalAmount() {
                const carSelect = document.getElementById('carSelect');
                const premiumNumberSelect = document.getElementById('premiumNumberSelect');
                const baseAmountInput = document.getElementById('baseAmount');

                let baseAmount = 0;

                // Add car price
                if (carSelect.value) {
                    const carOption = carSelect.options[carSelect.selectedIndex];
                    baseAmount += parseFloat(carOption.dataset.price || 0);
                }

                // Add premium number price
                if (premiumNumberSelect.value) {
                    const numberOption = premiumNumberSelect.options[premiumNumberSelect.selectedIndex];
                    baseAmount += parseFloat(numberOption.dataset.price || 0);
                }

                baseAmountInput.value = baseAmount;
                calculateTotal();
            }

            function calculateTotal() {
                const baseAmount = parseFloat(document.getElementById('baseAmount').value || 0);
                const discountAmount = parseFloat(document.getElementById('discountAmount').value || 0);
                const taxAmount = parseFloat(document.getElementById('taxAmount').value || 0);
                const totalAmountInput = document.getElementById('totalAmount');

                const total = baseAmount - discountAmount + taxAmount;
                totalAmountInput.value = total;

                calculateRemaining();
                calculateInstallment();
                updateSummary();
            }

            function calculateRemaining() {
                const totalAmount = parseFloat(document.getElementById('totalAmount').value || 0);
                const downPayment = parseFloat(document.getElementById('downPayment').value || 0);

                // Update remaining amount display if needed
                const remainingAmount = totalAmount - downPayment;

                calculateInstallment();
            }

            function calculateInstallment() {
                const totalAmount = parseFloat(document.getElementById('totalAmount').value || 0);
                const downPayment = parseFloat(document.getElementById('downPayment').value || 0);
                const installmentMonths = parseInt(document.getElementById('installmentMonths').value || 0);
                const interestRate = parseFloat(document.getElementById('interestRate').value || 0);
                const monthlyPaymentInput = document.getElementById('monthlyPayment');

                if (installmentMonths > 0) {
                    const principalAmount = totalAmount - downPayment;

                    if (interestRate > 0) {
                        // Calculate with interest using PMT formula
                        const monthlyRate = interestRate / 100 / 12;
                        const monthlyPayment = principalAmount * (monthlyRate * Math.pow(1 + monthlyRate, installmentMonths)) /
                                             (Math.pow(1 + monthlyRate, installmentMonths) - 1);
                        monthlyPaymentInput.value = monthlyPayment.toFixed(2);
                    } else {
                        // Simple division without interest
                        const monthlyPayment = principalAmount / installmentMonths;
                        monthlyPaymentInput.value = monthlyPayment.toFixed(2);
                    }
                } else {
                    monthlyPaymentInput.value = '';
                }
            }

            function updateSummary() {
                const contractType = document.querySelector('input[name="contract_type"]:checked')?.value;
                const paymentType = document.querySelector('input[name="payment_type"]:checked')?.value;
                const carSelect = document.getElementById('carSelect');
                const premiumNumberSelect = document.getElementById('premiumNumberSelect');
                const totalAmount = document.getElementById('totalAmount').value;
                const downPayment = document.getElementById('downPayment').value;
                const monthlyPayment = document.getElementById('monthlyPayment').value;
                const installmentMonths = document.getElementById('installmentMonths').value;
                const summarySection = document.getElementById('summarySection');
                const summaryDiv = document.getElementById('contractSummary');

                if (contractType && paymentType && totalAmount > 0) {
                    let summary = '<div class="row">';

                    // Contract type
                    let contractTypeText = '';
                    if (contractType === 'car_sale') contractTypeText = 'بيع سيارة';
                    else if (contractType === 'car_purchase') contractTypeText = 'شراء سيارة';
                    else if (contractType === 'premium_number_sale') contractTypeText = 'بيع رقم مميز';
                    else if (contractType === 'installment') contractTypeText = 'عقد تقسيط';

                    summary += '<div class="col-md-6"><strong>نوع العقد:</strong> ' + contractTypeText + '</div>';

                    // Payment type
                    let paymentTypeText = '';
                    if (paymentType === 'cash') paymentTypeText = 'نقدي';
                    else if (paymentType === 'installment') paymentTypeText = 'تقسيط';
                    else if (paymentType === 'bank_finance') paymentTypeText = 'تمويل بنكي';
                    else if (paymentType === 'lease') paymentTypeText = 'إيجار';
                    else if (paymentType === 'trade_in') paymentTypeText = 'استبدال';
                    else if (paymentType === 'mixed') paymentTypeText = 'مختلط';

                    summary += '<div class="col-md-6"><strong>طريقة الدفع:</strong> ' + paymentTypeText + '</div>';

                    // Items
                    if (carSelect.value) {
                        const carText = carSelect.options[carSelect.selectedIndex].text;
                        summary += '<div class="col-md-12 mt-2"><strong>السيارة:</strong> ' + carText + '</div>';
                    }

                    if (premiumNumberSelect.value) {
                        const numberText = premiumNumberSelect.options[premiumNumberSelect.selectedIndex].text;
                        summary += '<div class="col-md-12 mt-2"><strong>الرقم المميز:</strong> ' + numberText + '</div>';
                    }

                    // Financial summary
                    summary += '<div class="col-md-12 mt-3"><hr></div>';
                    summary += '<div class="col-md-6"><strong>المبلغ الإجمالي:</strong> ' + parseFloat(totalAmount).toLocaleString() + ' ر.ق</div>';

                    if (downPayment > 0) {
                        summary += '<div class="col-md-6"><strong>الدفعة المقدمة:</strong> ' + parseFloat(downPayment).toLocaleString() + ' ر.ق</div>';
                        const remaining = totalAmount - downPayment;
                        summary += '<div class="col-md-6"><strong>المبلغ المتبقي:</strong> ' + remaining.toLocaleString() + ' ر.ق</div>';
                    }

                    // Installment details
                    if ((paymentType === 'installment' || paymentType === 'bank_finance') && installmentMonths > 0 && monthlyPayment > 0) {
                        summary += '<div class="col-md-6"><strong>عدد الأقساط:</strong> ' + installmentMonths + ' شهر</div>';
                        summary += '<div class="col-md-12 mt-2"><h6 class="text-primary"><strong>القسط الشهري: ' + parseFloat(monthlyPayment).toLocaleString() + ' ر.ق</strong></h6></div>';
                    }

                    summary += '</div>';

                    summaryDiv.innerHTML = summary;
                    summarySection.style.display = 'block';
                } else {
                    summarySection.style.display = 'none';
                }
            }

            // Add event listeners
            document.querySelectorAll('input[name="payment_type"]').forEach(radio => {
                radio.addEventListener('change', updateSummary);
            });
        </script>

        <!-- Contract Guide Modal -->
        <div class="modal fade" id="contractGuideModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-question-circle"></i> دليل إنشاء العقود
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-file-contract text-primary"></i> أنواع العقود:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-car text-success"></i> <strong>بيع سيارة:</strong> عقد بيع السيارات للعملاء</li>
                                    <li><i class="fas fa-shopping-cart text-warning"></i> <strong>شراء سيارة:</strong> عقد شراء السيارات من العملاء</li>
                                    <li><i class="fas fa-hashtag text-info"></i> <strong>بيع رقم مميز:</strong> عقد بيع الأرقام المميزة</li>
                                    <li><i class="fas fa-calendar-alt text-secondary"></i> <strong>عقد تقسيط:</strong> عقود التقسيط المتخصصة</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-credit-card text-primary"></i> طرق الدفع:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-money-bill text-success"></i> <strong>نقدي:</strong> الدفع الفوري</li>
                                    <li><i class="fas fa-calendar-check text-warning"></i> <strong>تقسيط:</strong> أقساط شهرية</li>
                                    <li><i class="fas fa-university text-primary"></i> <strong>تمويل بنكي:</strong> تمويل من البنوك</li>
                                    <li><i class="fas fa-handshake text-info"></i> <strong>إيجار:</strong> عقود الإيجار</li>
                                    <li><i class="fas fa-exchange-alt text-secondary"></i> <strong>استبدال:</strong> استبدال السيارات</li>
                                    <li><i class="fas fa-coins text-dark"></i> <strong>مختلط:</strong> دمج طرق الدفع</li>
                                </ul>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-12">
                                <h6><i class="fas fa-lightbulb text-warning"></i> نصائح مهمة:</h6>
                                <div class="alert alert-info">
                                    <ul class="mb-0">
                                        <li>تأكد من اختيار العميل المناسب قبل البدء</li>
                                        <li>عند اختيار السيارة أو الرقم المميز، سيتم تحديث السعر تلقائياً</li>
                                        <li>في حالة التقسيط، سيتم حساب القسط الشهري تلقائياً</li>
                                        <li>يمكنك إضافة خصم أو ضريبة حسب الحاجة</li>
                                        <li>تأكد من مراجعة الملخص قبل الحفظ</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', customers=customers, cars=cars, premium_numbers=premium_numbers, today=today)

@app.route('/contracts/view/<int:contract_id>')
def view_contract(contract_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    contract = Contract.query.get_or_404(contract_id)

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>عرض العقد {{ contract.contract_number }} - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .contract-header {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border-radius: 15px;
                padding: 30px;
                margin-bottom: 30px;
            }
            .contract-section {
                border: 2px solid #e9ecef;
                border-radius: 15px;
                padding: 25px;
                margin-bottom: 25px;
                background: #fff;
            }
            .section-title {
                color: #007bff;
                border-bottom: 2px solid #007bff;
                padding-bottom: 10px;
                margin-bottom: 20px;
            }
            .info-row {
                padding: 8px 0;
                border-bottom: 1px solid #f8f9fa;
            }
            .info-label {
                font-weight: bold;
                color: #495057;
            }
            .status-badge {
                font-size: 1.1em;
                padding: 8px 15px;
            }
            .financial-highlight {
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                border-radius: 10px;
                padding: 20px;
                text-align: center;
            }
            @media print {
                .no-print { display: none !important; }
                .contract-section { break-inside: avoid; }
            }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('contracts') }}">العقود</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Contract Header -->
            <div class="contract-header text-center">
                <h1><i class="fas fa-file-contract"></i> العقد رقم: {{ contract.contract_number }}</h1>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <h5>نوع العقد</h5>
                        <p class="lead">
                            {% if contract.contract_type == 'car_sale' %}
                                <i class="fas fa-car"></i> بيع سيارة
                            {% elif contract.contract_type == 'car_purchase' %}
                                <i class="fas fa-shopping-cart"></i> شراء سيارة
                            {% elif contract.contract_type == 'premium_number_sale' %}
                                <i class="fas fa-hashtag"></i> بيع رقم مميز
                            {% elif contract.contract_type == 'installment' %}
                                <i class="fas fa-calendar-alt"></i> عقد تقسيط
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-4">
                        <h5>طريقة الدفع</h5>
                        <p class="lead">
                            {% if contract.payment_type == 'cash' %}
                                <i class="fas fa-money-bill"></i> نقدي
                            {% elif contract.payment_type == 'installment' %}
                                <i class="fas fa-calendar-check"></i> تقسيط
                            {% elif contract.payment_type == 'bank_finance' %}
                                <i class="fas fa-university"></i> تمويل بنكي
                            {% elif contract.payment_type == 'lease' %}
                                <i class="fas fa-handshake"></i> إيجار
                            {% elif contract.payment_type == 'trade_in' %}
                                <i class="fas fa-exchange-alt"></i> استبدال
                            {% elif contract.payment_type == 'mixed' %}
                                <i class="fas fa-coins"></i> مختلط
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-4">
                        <h5>حالة العقد</h5>
                        <p>
                            {% if contract.status == 'active' %}
                                <span class="badge bg-success status-badge">نشط</span>
                            {% elif contract.status == 'completed' %}
                                <span class="badge bg-primary status-badge">مكتمل</span>
                            {% elif contract.status == 'cancelled' %}
                                <span class="badge bg-danger status-badge">ملغي</span>
                            {% elif contract.status == 'draft' %}
                                <span class="badge bg-warning status-badge">مسودة</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Customer Information -->
                <div class="col-md-6">
                    <div class="contract-section">
                        <h4 class="section-title"><i class="fas fa-user"></i> معلومات العميل</h4>
                        <div class="info-row">
                            <span class="info-label">الاسم:</span>
                            <span class="float-end">{{ contract.customer.name_ar }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">الهاتف:</span>
                            <span class="float-end">{{ contract.customer.phone }}</span>
                        </div>
                        {% if contract.customer.email %}
                        <div class="info-row">
                            <span class="info-label">البريد الإلكتروني:</span>
                            <span class="float-end">{{ contract.customer.email }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Contract Details -->
                <div class="col-md-6">
                    <div class="contract-section">
                        <h4 class="section-title"><i class="fas fa-calendar"></i> تفاصيل العقد</h4>
                        <div class="info-row">
                            <span class="info-label">تاريخ العقد:</span>
                            <span class="float-end">{{ contract.contract_date.strftime('%Y-%m-%d') }}</span>
                        </div>
                        {% if contract.delivery_date %}
                        <div class="info-row">
                            <span class="info-label">تاريخ التسليم:</span>
                            <span class="float-end">{{ contract.delivery_date.strftime('%Y-%m-%d') }}</span>
                        </div>
                        {% endif %}
                        {% if contract.warranty_months > 0 %}
                        <div class="info-row">
                            <span class="info-label">فترة الضمان:</span>
                            <span class="float-end">{{ contract.warranty_months }} شهر</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Items Section -->
            {% if contract.car or contract.premium_number %}
            <div class="contract-section">
                <h4 class="section-title"><i class="fas fa-list"></i> العناصر المباعة</h4>

                {% if contract.car %}
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-car"></i> السيارة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>الماركة:</strong> {{ contract.car.make }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>الموديل:</strong> {{ contract.car.model }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>السنة:</strong> {{ contract.car.year }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>اللون:</strong> {{ contract.car.color or 'غير محدد' }}
                                    </div>
                                </div>
                                {% if contract.car.vin_number %}
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <strong>رقم الشاسيه:</strong> {{ contract.car.vin_number }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>رقم اللوحة:</strong> {{ contract.car.license_plate or 'غير محدد' }}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if contract.premium_number %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5><i class="fas fa-hashtag"></i> الرقم المميز</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>الرقم:</strong> {{ contract.premium_number.number }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>الفئة:</strong> {{ contract.premium_number.category }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Financial Details -->
            <div class="contract-section">
                <h4 class="section-title"><i class="fas fa-calculator"></i> التفاصيل المالية</h4>

                <div class="row">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-row">
                                    <span class="info-label">المبلغ الإجمالي:</span>
                                    <span class="float-end">{{ "{:,.0f}".format(contract.total_amount) }} ر.ق</span>
                                </div>
                                {% if contract.down_payment > 0 %}
                                <div class="info-row">
                                    <span class="info-label">الدفعة المقدمة:</span>
                                    <span class="float-end">{{ "{:,.0f}".format(contract.down_payment) }} ر.ق</span>
                                </div>
                                {% endif %}
                                {% if contract.discount_amount > 0 %}
                                <div class="info-row">
                                    <span class="info-label">مبلغ الخصم:</span>
                                    <span class="float-end text-success">{{ "{:,.0f}".format(contract.discount_amount) }} ر.ق</span>
                                </div>
                                {% endif %}
                                {% if contract.tax_amount > 0 %}
                                <div class="info-row">
                                    <span class="info-label">مبلغ الضريبة:</span>
                                    <span class="float-end">{{ "{:,.0f}".format(contract.tax_amount) }} ر.ق</span>
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {% if contract.remaining_amount > 0 %}
                                <div class="info-row">
                                    <span class="info-label">المبلغ المتبقي:</span>
                                    <span class="float-end">{{ "{:,.0f}".format(contract.remaining_amount) }} ر.ق</span>
                                </div>
                                {% endif %}
                                {% if contract.monthly_payment > 0 %}
                                <div class="info-row">
                                    <span class="info-label">القسط الشهري:</span>
                                    <span class="float-end">{{ "{:,.0f}".format(contract.monthly_payment) }} ر.ق</span>
                                </div>
                                {% endif %}
                                {% if contract.installment_months > 0 %}
                                <div class="info-row">
                                    <span class="info-label">عدد الأقساط:</span>
                                    <span class="float-end">{{ contract.installment_months }} شهر</span>
                                </div>
                                {% endif %}
                                {% if contract.interest_rate > 0 %}
                                <div class="info-row">
                                    <span class="info-label">معدل الفائدة:</span>
                                    <span class="float-end">{{ contract.interest_rate }}% سنوياً</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="financial-highlight">
                            <h5>المبلغ الإجمالي</h5>
                            <h2>{{ "{:,.0f}".format(contract.total_amount) }} ر.ق</h2>
                            {% if contract.monthly_payment > 0 %}
                                <hr style="border-color: rgba(255,255,255,0.3);">
                                <h6>القسط الشهري</h6>
                                <h4>{{ "{:,.0f}".format(contract.monthly_payment) }} ر.ق</h4>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            {% if contract.special_conditions or contract.notes or contract.witness_name %}
            <div class="contract-section">
                <h4 class="section-title"><i class="fas fa-info-circle"></i> معلومات إضافية</h4>

                {% if contract.witness_name %}
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="info-row">
                            <span class="info-label">اسم الشاهد:</span>
                            <span class="float-end">{{ contract.witness_name }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        {% if contract.witness_id %}
                        <div class="info-row">
                            <span class="info-label">رقم هوية الشاهد:</span>
                            <span class="float-end">{{ contract.witness_id }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                {% if contract.special_conditions %}
                <div class="mb-3">
                    <h6><i class="fas fa-exclamation-triangle"></i> الشروط الخاصة:</h6>
                    <div class="bg-light p-3 rounded">{{ contract.special_conditions }}</div>
                </div>
                {% endif %}

                {% if contract.notes %}
                <div class="mb-3">
                    <h6><i class="fas fa-sticky-note"></i> ملاحظات:</h6>
                    <div class="bg-light p-3 rounded">{{ contract.notes }}</div>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Installment Schedule -->
            {% if contract.installment_payments %}
            <div class="contract-section">
                <h4 class="section-title"><i class="fas fa-calendar-check"></i> جدول الأقساط</h4>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم القسط</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>المبلغ المطلوب</th>
                                <th>المبلغ المدفوع</th>
                                <th>تاريخ الدفع</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in contract.installment_payments %}
                            <tr>
                                <td>{{ payment.installment_number }}</td>
                                <td>{{ payment.due_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ "{:,.0f}".format(payment.amount) }} ر.ق</td>
                                <td>{{ "{:,.0f}".format(payment.paid_amount) }} ر.ق</td>
                                <td>{{ payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else '-' }}</td>
                                <td>
                                    {% if payment.status == 'paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                    {% elif payment.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                    {% elif payment.status == 'overdue' %}
                                        <span class="badge bg-danger">متأخر</span>
                                    {% elif payment.status == 'partial' %}
                                        <span class="badge bg-info">جزئي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="text-center mb-4 no-print">
                <a href="{{ url_for('contracts') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للعقود
                </a>
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print"></i> طباعة العقد
                </button>
                <a href="{{ url_for('generate_contract_pdf', contract_id=contract.id) }}" class="btn btn-success">
                    <i class="fas fa-file-pdf"></i> تحميل PDF
                </a>
                {% if contract.status == 'draft' %}
                <a href="{{ url_for('edit_contract', contract_id=contract.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> تعديل العقد
                </a>
                {% endif %}
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''', contract=contract)

@app.route('/contracts/edit/<int:contract_id>', methods=['GET', 'POST'])
def edit_contract(contract_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    contract = Contract.query.get_or_404(contract_id)

    if request.method == 'POST':
        try:
            from datetime import datetime

            # Update contract fields
            contract.contract_type = request.form.get('contract_type', contract.contract_type)
            contract.payment_type = request.form.get('payment_type', contract.payment_type)
            contract.customer_id = request.form.get('customer_id', type=int) or contract.customer_id

            # Financial details
            contract.total_amount = request.form.get('total_amount', type=float) or contract.total_amount
            contract.down_payment = request.form.get('down_payment', type=float) or 0
            contract.monthly_payment = request.form.get('monthly_payment', type=float) or 0
            contract.installment_months = request.form.get('installment_months', type=int) or 0
            contract.interest_rate = request.form.get('interest_rate', type=float) or 0
            contract.discount_amount = request.form.get('discount_amount', type=float) or 0
            contract.tax_amount = request.form.get('tax_amount', type=float) or 0

            # Calculate remaining amount
            contract.remaining_amount = contract.total_amount - contract.down_payment - contract.discount_amount + contract.tax_amount

            # Contract details
            if request.form.get('contract_date'):
                contract.contract_date = datetime.strptime(request.form['contract_date'], '%Y-%m-%d').date()
            if request.form.get('delivery_date'):
                contract.delivery_date = datetime.strptime(request.form['delivery_date'], '%Y-%m-%d').date()

            contract.warranty_months = request.form.get('warranty_months', type=int) or 0
            contract.insurance_required = 'insurance_required' in request.form
            contract.registration_included = 'registration_included' in request.form
            contract.special_conditions = request.form.get('special_conditions', '')
            contract.notes = request.form.get('notes', '')
            contract.witness_name = request.form.get('witness_name', '')
            contract.witness_id = request.form.get('witness_id', '')

            db.session.commit()
            flash('تم تحديث العقد بنجاح', 'success')
            return redirect(url_for('view_contract', contract_id=contract.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث العقد: {str(e)}', 'error')

    # GET request - show form
    customers = Customer.query.all()
    cars = Car.query.filter_by(status='available').all()
    premium_numbers = PremiumNumber.query.filter_by(status='available').all()

    # Add current car/number to lists if they exist
    if contract.car and contract.car not in cars:
        cars.append(contract.car)
    if contract.premium_number and contract.premium_number not in premium_numbers:
        premium_numbers.append(contract.premium_number)

    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تعديل العقد - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('contracts') }}">العقود</a>
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-edit"></i> تعديل العقد {{ contract.contract_number }}</h2>
                <div>
                    <a href="{{ url_for('view_contract', contract_id=contract.id) }}" class="btn btn-info">
                        <i class="fas fa-eye"></i> عرض العقد
                    </a>
                    <a href="{{ url_for('contracts') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للعقود
                    </a>
                </div>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST">
                <!-- Basic Information -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-info-circle"></i> المعلومات الأساسية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">نوع العقد</label>
                                <select class="form-select" name="contract_type" required>
                                    <option value="car_sale" {{ 'selected' if contract.contract_type == 'car_sale' else '' }}>بيع سيارة</option>
                                    <option value="premium_number_sale" {{ 'selected' if contract.contract_type == 'premium_number_sale' else '' }}>بيع رقم مميز</option>
                                    <option value="combined_sale" {{ 'selected' if contract.contract_type == 'combined_sale' else '' }}>عقد مدمج</option>
                                    <option value="installment" {{ 'selected' if contract.contract_type == 'installment' else '' }}>عقد تقسيط</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" name="payment_type" required>
                                    <option value="cash" {{ 'selected' if contract.payment_type == 'cash' else '' }}>نقدي</option>
                                    <option value="installment" {{ 'selected' if contract.payment_type == 'installment' else '' }}>تقسيط</option>
                                    <option value="bank_finance" {{ 'selected' if contract.payment_type == 'bank_finance' else '' }}>تمويل بنكي</option>
                                    <option value="lease" {{ 'selected' if contract.payment_type == 'lease' else '' }}>إيجار</option>
                                    <option value="trade_in" {{ 'selected' if contract.payment_type == 'trade_in' else '' }}>استبدال</option>
                                    <option value="mixed" {{ 'selected' if contract.payment_type == 'mixed' else '' }}>مختلط</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label class="form-label">العميل</label>
                                <select class="form-select" name="customer_id" required>
                                    {% for customer in customers %}
                                    <option value="{{ customer.id }}" {{ 'selected' if customer.id == contract.customer_id else '' }}>
                                        {{ customer.name_ar }} - {{ customer.phone }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Details -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-money-bill"></i> التفاصيل المالية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">المبلغ الإجمالي (ر.ق)</label>
                                <input type="number" class="form-control" name="total_amount" step="0.01" value="{{ contract.total_amount }}" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">الدفعة المقدمة (ر.ق)</label>
                                <input type="number" class="form-control" name="down_payment" step="0.01" value="{{ contract.down_payment or 0 }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">مبلغ الخصم (ر.ق)</label>
                                <input type="number" class="form-control" name="discount_amount" step="0.01" value="{{ contract.discount_amount or 0 }}">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <label class="form-label">مبلغ الضريبة (ر.ق)</label>
                                <input type="number" class="form-control" name="tax_amount" step="0.01" value="{{ contract.tax_amount or 0 }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">القسط الشهري (ر.ق)</label>
                                <input type="number" class="form-control" name="monthly_payment" step="0.01" value="{{ contract.monthly_payment or 0 }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">عدد الأقساط</label>
                                <input type="number" class="form-control" name="installment_months" value="{{ contract.installment_months or 0 }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="text-center">
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <a href="{{ url_for('view_contract', contract_id=contract.id) }}" class="btn btn-secondary btn-lg ms-2">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''', contract=contract, customers=customers, cars=cars, premium_numbers=premium_numbers)

@app.route('/contracts/delete/<int:contract_id>', methods=['POST'])
def delete_contract(contract_id):
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})

    try:
        contract = Contract.query.get_or_404(contract_id)

        # Delete related installment payments first
        InstallmentPayment.query.filter_by(contract_id=contract_id).delete()

        # Delete the contract
        db.session.delete(contract)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حذف العقد بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/contracts/pdf/<int:contract_id>')
def generate_contract_pdf(contract_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    contract = Contract.query.get_or_404(contract_id)

    # For now, return a simple HTML version that can be printed
    # In a real application, you would use a PDF library like ReportLab or WeasyPrint
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>عقد رقم {{ contract.contract_number }}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            @media print {
                .no-print { display: none !important; }
                body { font-size: 12px; }
                .container { max-width: 100% !important; }
            }
            .contract-header {
                text-align: center;
                border-bottom: 3px solid #007bff;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }
            .contract-section {
                margin-bottom: 25px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
            .signature-section {
                margin-top: 50px;
                display: flex;
                justify-content: space-between;
            }
            .signature-box {
                width: 200px;
                height: 80px;
                border: 1px solid #000;
                text-align: center;
                padding-top: 60px;
            }
        </style>
    </head>
    <body>
        <div class="container mt-4">
            <!-- Print Button -->
            <div class="no-print text-center mb-4">
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print"></i> طباعة العقد
                </button>
                <a href="{{ url_for('contracts') }}" class="btn btn-secondary ms-2">
                    <i class="fas fa-arrow-right"></i> العودة للعقود
                </a>
            </div>

            <!-- Contract Header -->
            <div class="contract-header">
                <h1>معرض قطر للسيارات</h1>
                <h3>عقد {{ 'بيع سيارة' if contract.contract_type == 'car_sale' else 'بيع رقم مميز' if contract.contract_type == 'premium_number_sale' else 'مدمج' }}</h3>
                <h4>رقم العقد: {{ contract.contract_number }}</h4>
                <p>التاريخ: {{ contract.contract_date.strftime('%Y/%m/%d') if contract.contract_date else 'غير محدد' }}</p>
            </div>

            <!-- Parties Information -->
            <div class="contract-section">
                <h5>أطراف العقد:</h5>
                <div class="row">
                    <div class="col-md-6">
                        <strong>الطرف الأول (البائع):</strong><br>
                        معرض قطر للسيارات<br>
                        العنوان: الدوحة، قطر<br>
                        الهاتف: +974 XXXX XXXX
                    </div>
                    <div class="col-md-6">
                        <strong>الطرف الثاني (المشتري):</strong><br>
                        الاسم: {{ contract.customer.name_ar }}<br>
                        الهاتف: {{ contract.customer.phone }}<br>
                        {% if contract.customer.email %}البريد الإلكتروني: {{ contract.customer.email }}<br>{% endif %}
                        {% if contract.customer.address %}العنوان: {{ contract.customer.address }}{% endif %}
                    </div>
                </div>
            </div>

            <!-- Contract Details -->
            {% if contract.car %}
            <div class="contract-section">
                <h5>تفاصيل السيارة:</h5>
                <div class="row">
                    <div class="col-md-6">
                        <strong>الماركة:</strong> {{ contract.car.make }}<br>
                        <strong>الموديل:</strong> {{ contract.car.model }}<br>
                        <strong>السنة:</strong> {{ contract.car.year }}<br>
                        <strong>اللون:</strong> {{ contract.car.color or 'غير محدد' }}
                    </div>
                    <div class="col-md-6">
                        <strong>رقم الشاسيه:</strong> {{ contract.car.vin_number or 'غير محدد' }}<br>
                        <strong>رقم اللوحة:</strong> {{ contract.car.license_plate or 'غير محدد' }}<br>
                        <strong>المسافة:</strong> {{ "{:,}".format(contract.car.mileage) + " كم" if contract.car.mileage else 'غير محدد' }}<br>
                        <strong>الحالة:</strong> {{ contract.car.condition or 'غير محدد' }}
                    </div>
                </div>
            </div>
            {% endif %}

            {% if contract.premium_number %}
            <div class="contract-section">
                <h5>تفاصيل الرقم المميز:</h5>
                <div class="text-center">
                    <h2 class="text-primary">{{ contract.premium_number.number }}</h2>
                    <p><strong>الفئة:</strong> {{ contract.premium_number.category }}</p>
                </div>
            </div>
            {% endif %}

            <!-- Financial Details -->
            <div class="contract-section">
                <h5>التفاصيل المالية:</h5>
                <table class="table table-bordered">
                    <tr>
                        <td><strong>المبلغ الإجمالي:</strong></td>
                        <td>{{ "{:,.0f}".format(contract.total_amount) }} ريال قطري</td>
                    </tr>
                    {% if contract.down_payment and contract.down_payment > 0 %}
                    <tr>
                        <td><strong>الدفعة المقدمة:</strong></td>
                        <td>{{ "{:,.0f}".format(contract.down_payment) }} ريال قطري</td>
                    </tr>
                    {% endif %}
                    {% if contract.discount_amount and contract.discount_amount > 0 %}
                    <tr>
                        <td><strong>مبلغ الخصم:</strong></td>
                        <td>{{ "{:,.0f}".format(contract.discount_amount) }} ريال قطري</td>
                    </tr>
                    {% endif %}
                    {% if contract.tax_amount and contract.tax_amount > 0 %}
                    <tr>
                        <td><strong>مبلغ الضريبة:</strong></td>
                        <td>{{ "{:,.0f}".format(contract.tax_amount) }} ريال قطري</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>المبلغ المتبقي:</strong></td>
                        <td>{{ "{:,.0f}".format(contract.remaining_amount) }} ريال قطري</td>
                    </tr>
                    <tr>
                        <td><strong>طريقة الدفع:</strong></td>
                        <td>
                            {% if contract.payment_type == 'cash' %}نقدي
                            {% elif contract.payment_type == 'installment' %}تقسيط
                            {% elif contract.payment_type == 'bank_finance' %}تمويل بنكي
                            {% elif contract.payment_type == 'lease' %}إيجار
                            {% elif contract.payment_type == 'trade_in' %}استبدال
                            {% elif contract.payment_type == 'mixed' %}مختلط
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Terms and Conditions -->
            <div class="contract-section">
                <h5>الشروط والأحكام:</h5>
                <ul>
                    <li>يتعهد المشتري بدفع المبلغ المتفق عليه في المواعيد المحددة</li>
                    <li>يحق للبائع استرداد البضاعة في حالة عدم الدفع</li>
                    {% if contract.warranty_months and contract.warranty_months > 0 %}
                    <li>فترة الضمان: {{ contract.warranty_months }} شهر</li>
                    {% endif %}
                    {% if contract.insurance_required %}
                    <li>التأمين مطلوب على المشتري</li>
                    {% endif %}
                    {% if contract.registration_included %}
                    <li>التسجيل مشمول في السعر</li>
                    {% endif %}
                    {% if contract.special_conditions %}
                    <li>{{ contract.special_conditions }}</li>
                    {% endif %}
                </ul>
            </div>

            <!-- Signatures -->
            <div class="signature-section">
                <div class="text-center">
                    <div class="signature-box">توقيع البائع</div>
                    <p class="mt-2">معرض قطر للسيارات</p>
                </div>
                <div class="text-center">
                    <div class="signature-box">توقيع المشتري</div>
                    <p class="mt-2">{{ contract.customer.name_ar }}</p>
                </div>
                {% if contract.witness_name %}
                <div class="text-center">
                    <div class="signature-box">توقيع الشاهد</div>
                    <p class="mt-2">{{ contract.witness_name }}</p>
                </div>
                {% endif %}
            </div>

            <div class="text-center mt-4">
                <small class="text-muted">تم إنشاء هذا العقد بتاريخ {{ contract.created_at.strftime('%Y/%m/%d %H:%M') }}</small>
            </div>
        </div>
    </body>
    </html>
    ''', contract=contract)

def setup_database():
    """Setup database with sample data"""
    with app.app_context():
        # Create tables
        db.create_all()

        # Create admin user if not exists
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                password_hash=generate_password_hash('admin123'),
                role='admin',
                is_active=True
            )
            db.session.add(admin)

        # Create sample customer if not exists
        customer = Customer.query.first()
        if not customer:
            customer = Customer(
                name_ar='أحمد محمد',
                phone='+974 5555 1234',
                email='<EMAIL>'
            )
            db.session.add(customer)

        # Create sample cars if not exist
        if Car.query.count() == 0:
            cars = [
                Car(make='تويوتا', model='كامري', year=2023, price=120000, status='available'),
                Car(make='نيسان', model='التيما', year=2022, price=95000, status='available'),
                Car(make='هوندا', model='أكورد', year=2023, price=110000, status='available'),
                Car(make='لكزس', model='ES350', year=2023, price=180000, status='available'),
            ]
            for car in cars:
                db.session.add(car)

        # Create sample premium numbers if not exist
        if PremiumNumber.query.count() == 0:
            premium_numbers = [
                PremiumNumber(number='123456', category='VIP', price=50000, status='available'),
                PremiumNumber(number='777777', category='مميز', price=75000, status='available'),
                PremiumNumber(number='100100', category='عادي', price=25000, status='available'),
                PremiumNumber(number='999999', category='VIP', price=100000, status='available'),
                PremiumNumber(number='555555', category='VIP', price=85000, status='available'),
                PremiumNumber(number='888888', category='مميز', price=90000, status='available'),
                PremiumNumber(number='111111', category='VIP', price=95000, status='available'),
                PremiumNumber(number='222222', category='مميز', price=70000, status='available'),
                PremiumNumber(number='333333', category='مميز', price=65000, status='available'),
                PremiumNumber(number='444444', category='عادي', price=35000, status='available'),
                PremiumNumber(number='666666', category='مميز', price=80000, status='available'),
                PremiumNumber(number='101010', category='عادي', price=30000, status='available'),
                PremiumNumber(number='202020', category='عادي', price=28000, status='available'),
                PremiumNumber(number='303030', category='عادي', price=32000, status='available'),
                PremiumNumber(number='505050', category='عادي', price=35000, status='available'),
            ]
            for number in premium_numbers:
                db.session.add(number)
        else:
            # Reset any numbers that might be stuck in auction status
            stuck_numbers = PremiumNumber.query.filter_by(status='auction').all()
            for number in stuck_numbers:
                # Check if there's an active auction for this number
                active_auction = Auction.query.filter_by(
                    premium_number_id=number.id,
                    status='active'
                ).first()
                if not active_auction:
                    number.status = 'available'
                    print(f"✅ تم إعادة تعيين حالة الرقم {number.number} إلى متاح")

        db.session.commit()
        print("✅ تم إعداد قاعدة البيانات بنجاح")

if __name__ == '__main__':
    print("🚀 نظام معرض قطر للسيارات - النسخة العاملة")
    print("🔧 تم حل مشكلة config.py ودعم الأرقام المميزة")
    print("=" * 60)

    try:
        # Setup database
        setup_database()

        print("=" * 60)
        print("🎉 نظام معرض قطر للسيارات - جاهز للتشغيل!")
        print("=" * 60)
        print("🌐 الخادم: http://127.0.0.1:9898")
        print("🔐 المستخدم: admin")
        print("🔑 كلمة المرور: admin123")
        print("✨ الميزات الجديدة:")
        print("   🚗 بيع السيارات")
        print("   🔢 بيع الأرقام المميزة")
        print("   🎯 العقود المدمجة")
        print("   💳 طرق دفع متعددة")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)

        # Start server
        app.run(host='127.0.0.1', port=9898, debug=False, use_reloader=False)

    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")