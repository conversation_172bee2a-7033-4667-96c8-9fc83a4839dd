#!/usr/bin/env python3
"""
Qatar Car Showroom - Main Application Entry Point
Enhanced with Premium Numbers Support in Contracts and Installments
"""

import os
import sys

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import from working_app.py instead of app module
from working_app import app, db, User
from werkzeug.security import generate_password_hash
import webbrowser
import threading
import time

def setup_admin():
    """Setup admin user"""
    try:
        with app.app_context():
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    role='admin',
                    is_active=True
                )
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء مستخدم admin")
            else:
                print("✅ مستخدم admin موجود")
    except Exception as e:
        print(f"⚠️ تحذير في إعداد المستخدم: {e}")

def open_browser_delayed():
    """Open browser after delay"""
    time.sleep(3)
    try:
        webbrowser.open('http://127.0.0.1:9898')
        print("🌐 تم فتح المتصفح تلقائياً")
    except:
        print("💡 افتح المتصفح يدوياً على: http://127.0.0.1:9898")

def main():
    """Main application function"""
    print("🚀 نظام معرض قطر للسيارات - النسخة الكاملة مع الأقساط")
    print("=" * 70)

    try:
        with app.app_context():
            db.create_all()
            setup_admin()

        print("=" * 60)
        print("🎉 نظام معرض قطر للسيارات - النسخة الكاملة")
        print("=" * 60)
        print("🌐 الخادم يعمل على: http://127.0.0.1:9898")
        print("🔐 بيانات الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("✨ الميزات المتاحة:")
        print("   🚗 إدارة السيارات")
        print("   🔢 إدارة الأرقام المميزة")
        print("   🎯 نظام المزادات المتقدم")
        print("   📋 إدارة العقود")
        print("   💳 نظام الأقساط الكامل")
        print("   📊 التقارير والإحصائيات")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)

        # Open browser in background
        threading.Thread(target=open_browser_delayed, daemon=True).start()

        # Start server
        app.run(host='127.0.0.1', port=9898, debug=False, use_reloader=False)

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
